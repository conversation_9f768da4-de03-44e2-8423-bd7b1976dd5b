/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5Ctest%5Cnextjs-frontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5Ctest%5Cnextjs-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5Ctest%5Cnextjs-frontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5Ctest%5Cnextjs-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)),\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/_not-found/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5Ctest%5Cnextjs-frontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5Ctest%5Cnextjs-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/providers.tsx */ \"(ssr)/./app/providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2xha3NoaXRhLnZ5YXMlNUMlNUNEZXNrdG9wJTVDJTVDdGVzdCU1QyU1Q25leHRqcy1mcm9udGVuZCU1QyU1Q2FwcCU1QyU1Q3Byb3ZpZGVycy50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJQcm92aWRlcnMlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDbGFrc2hpdGEudnlhcyU1QyU1Q0Rlc2t0b3AlNUMlNUN0ZXN0JTVDJTVDbmV4dGpzLWZyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2ZvbnQlNUMlNUNnb29nbGUlNUMlNUN0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMmFwcCU1QyU1QyU1QyU1Q2xheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2xha3NoaXRhLnZ5YXMlNUMlNUNEZXNrdG9wJTVDJTVDdGVzdCU1QyU1Q25leHRqcy1mcm9udGVuZCU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrSkFBZ0oiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kYWRwLWZyb250ZW5kLz9mMmYyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiUHJvdmlkZXJzXCJdICovIFwiQzpcXFxcVXNlcnNcXFxcbGFrc2hpdGEudnlhc1xcXFxEZXNrdG9wXFxcXHRlc3RcXFxcbmV4dGpzLWZyb250ZW5kXFxcXGFwcFxcXFxwcm92aWRlcnMudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/providers.tsx":
/*!***************************!*\
  !*** ./app/providers.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _lib_auth_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth-context */ \"(ssr)/./lib/auth-context.tsx\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\n\nfunction Providers({ children }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.QueryClient({\n            defaultOptions: {\n                queries: {\n                    staleTime: 60 * 1000,\n                    retry: 1\n                }\n            }\n        }));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.QueryClientProvider, {\n        client: queryClient,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_auth_context__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n            children: [\n                children,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n                    position: \"top-right\",\n                    toastOptions: {\n                        duration: 5000,\n                        style: {\n                            background: \"#fff\",\n                            color: \"#333\",\n                            border: \"1px solid #e2e8f0\",\n                            borderRadius: \"8px\",\n                            boxShadow: \"0 4px 20px rgba(0,0,0,0.1)\"\n                        },\n                        success: {\n                            iconTheme: {\n                                primary: \"#38a169\",\n                                secondary: \"#fff\"\n                            }\n                        },\n                        error: {\n                            iconTheme: {\n                                primary: \"#e53e3e\",\n                                secondary: \"#fff\"\n                            }\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\providers.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\providers.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\providers.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/api-client.ts":
/*!***************************!*\
  !*** ./lib/api-client.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   api: () => (/* binding */ api),\n/* harmony export */   apiClient: () => (/* binding */ apiClient)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\n// Create axios instance with base configuration\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: \"http://localhost:5000\" || 0,\n    timeout: 30000,\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n});\n// Request interceptor to add auth token\napiClient.interceptors.request.use((config)=>{\n    const token = localStorage.getItem(\"auth_token\");\n    if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Response interceptor to handle common errors\napiClient.interceptors.response.use((response)=>{\n    return response;\n}, (error)=>{\n    if (error.response?.status === 401) {\n        // Unauthorized - clear token and redirect to login\n        localStorage.removeItem(\"auth_token\");\n        if (false) {}\n    }\n    return Promise.reject(error);\n});\n// API helper functions\nconst api = {\n    // Authentication\n    auth: {\n        login: (credentials)=>apiClient.post(\"/auth/login\", credentials),\n        logout: ()=>apiClient.post(\"/auth/logout\"),\n        me: ()=>apiClient.get(\"/auth/me\"),\n        changePassword: (data)=>apiClient.post(\"/auth/change-password\", data)\n    },\n    // User management\n    users: {\n        list: (params)=>apiClient.get(\"/users\", {\n                params\n            }),\n        create: (userData)=>apiClient.post(\"/users\", userData),\n        update: (id, userData)=>apiClient.put(`/users/${id}`, userData),\n        delete: (id)=>apiClient.delete(`/users/${id}`),\n        get: (id)=>apiClient.get(`/users/${id}`)\n    },\n    // File management\n    files: {\n        browse: (path)=>apiClient.get(\"/files/browse\", {\n                params: {\n                    path\n                }\n            }),\n        upload: (formData)=>apiClient.post(\"/files/upload\", formData, {\n                headers: {\n                    \"Content-Type\": \"multipart/form-data\"\n                }\n            }),\n        delete: (path)=>apiClient.delete(\"/files\", {\n                params: {\n                    path\n                }\n            })\n    },\n    // Annotation tasks\n    annotations: {\n        list: (params)=>apiClient.get(\"/annotations\", {\n                params\n            }),\n        get: (id)=>apiClient.get(`/annotations/${id}`),\n        create: (data)=>apiClient.post(\"/annotations\", data),\n        update: (id, data)=>apiClient.put(`/annotations/${id}`, data),\n        save: (id, annotationData)=>apiClient.post(`/annotations/${id}/save`, annotationData),\n        submit: (id)=>apiClient.post(`/annotations/${id}/submit`)\n    },\n    // Audit tasks\n    audits: {\n        list: (params)=>apiClient.get(\"/audits\", {\n                params\n            }),\n        get: (id)=>apiClient.get(`/audits/${id}`),\n        approve: (id, feedback)=>apiClient.post(`/audits/${id}/approve`, {\n                feedback\n            }),\n        reject: (id, feedback)=>apiClient.post(`/audits/${id}/reject`, {\n                feedback\n            })\n    },\n    // Data connectors\n    connectors: {\n        nas: {\n            connect: (config)=>apiClient.post(\"/connectors/nas/connect\", config),\n            disconnect: ()=>apiClient.post(\"/connectors/nas/disconnect\"),\n            status: ()=>apiClient.get(\"/connectors/nas/status\"),\n            browse: (path)=>apiClient.get(\"/connectors/nas/browse\", {\n                    params: {\n                        path\n                    }\n                })\n        },\n        googleDrive: {\n            configure: (config)=>apiClient.post(\"/connectors/google-drive/configure\", config),\n            status: ()=>apiClient.get(\"/connectors/google-drive/status\"),\n            reset: ()=>apiClient.post(\"/connectors/google-drive/reset\")\n        }\n    },\n    // Dashboard\n    dashboard: {\n        stats: ()=>apiClient.get(\"/dashboard/stats\"),\n        userStats: (userId)=>apiClient.get(\"/dashboard/user-stats\", {\n                params: {\n                    user_id: userId\n                }\n            })\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/api-client.ts\n");

/***/ }),

/***/ "(ssr)/./lib/auth-context.tsx":
/*!******************************!*\
  !*** ./lib/auth-context.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _api_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./api-client */ \"(ssr)/./lib/api-client.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check for existing session on mount\n        checkSession();\n    }, []);\n    const checkSession = async ()=>{\n        try {\n            const token = localStorage.getItem(\"auth_token\");\n            if (!token) {\n                setLoading(false);\n                return;\n            }\n            const response = await _api_client__WEBPACK_IMPORTED_MODULE_2__.apiClient.get(\"/auth/me\");\n            if (response.data.success) {\n                setUser(response.data.data.user);\n                setSession(response.data.data);\n            } else {\n                localStorage.removeItem(\"auth_token\");\n            }\n        } catch (error) {\n            console.error(\"Session check failed:\", error);\n            localStorage.removeItem(\"auth_token\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const login = async (username, password)=>{\n        try {\n            setLoading(true);\n            const response = await _api_client__WEBPACK_IMPORTED_MODULE_2__.apiClient.post(\"/auth/login\", {\n                username,\n                password\n            });\n            if (response.data.success) {\n                const sessionData = response.data.data;\n                setUser(sessionData.user);\n                setSession(sessionData);\n                localStorage.setItem(\"auth_token\", sessionData.token);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"Login successful!\");\n                return true;\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(response.data.message || \"Login failed\");\n                return false;\n            }\n        } catch (error) {\n            const message = error.response?.data?.message || \"Login failed\";\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(message);\n            return false;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const logout = ()=>{\n        setUser(null);\n        setSession(null);\n        localStorage.removeItem(\"auth_token\");\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"Logged out successfully\");\n    };\n    const refreshSession = async ()=>{\n        await checkSession();\n    };\n    const value = {\n        user,\n        session,\n        loading,\n        login,\n        logout,\n        refreshSession\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\lib\\\\auth-context.tsx\",\n        lineNumber: 101,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/auth-context.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"dce4b479c1e7\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kYWRwLWZyb250ZW5kLy4vYXBwL2dsb2JhbHMuY3NzP2ZhMjkiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJkY2U0YjQ3OWMxZTdcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./providers */ \"(rsc)/./app/providers.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"DADP - Data Annotation & Delivery Platform\",\n    description: \"Human Augmented AI Agents platform with HITL for precise AI training Datasets & Enterprise Data Management.\",\n    icons: {\n        icon: \"/favicon.png\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"stylesheet\",\n                        href: \"https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\layout.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\layout.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/providers.tsx":
/*!***************************!*\
  !*** ./app/providers.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\test\nextjs-frontend\app\providers.tsx#Providers`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/@tanstack","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/react-hot-toast","vendor-chunks/get-intrinsic","vendor-chunks/form-data","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/goober","vendor-chunks/@swc","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5Ctest%5Cnextjs-frontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5Ctest%5Cnextjs-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();