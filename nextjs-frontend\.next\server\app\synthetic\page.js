/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/synthetic/page";
exports.ids = ["app/synthetic/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsynthetic%2Fpage&page=%2Fsynthetic%2Fpage&appPaths=%2Fsynthetic%2Fpage&pagePath=private-next-app-dir%2Fsynthetic%2Fpage.tsx&appDir=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5Ctest%5Cnextjs-frontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5Ctest%5Cnextjs-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsynthetic%2Fpage&page=%2Fsynthetic%2Fpage&appPaths=%2Fsynthetic%2Fpage&pagePath=private-next-app-dir%2Fsynthetic%2Fpage.tsx&appDir=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5Ctest%5Cnextjs-frontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5Ctest%5Cnextjs-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'synthetic',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/synthetic/page.tsx */ \"(rsc)/./app/synthetic/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/synthetic/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/synthetic/page\",\n        pathname: \"/synthetic\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsynthetic%2Fpage&page=%2Fsynthetic%2Fpage&appPaths=%2Fsynthetic%2Fpage&pagePath=private-next-app-dir%2Fsynthetic%2Fpage.tsx&appDir=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5Ctest%5Cnextjs-frontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5Ctest%5Cnextjs-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/providers.tsx */ \"(ssr)/./app/providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2xha3NoaXRhLnZ5YXMlNUMlNUNEZXNrdG9wJTVDJTVDdGVzdCU1QyU1Q25leHRqcy1mcm9udGVuZCU1QyU1Q2FwcCU1QyU1Q3Byb3ZpZGVycy50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJQcm92aWRlcnMlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDbGFrc2hpdGEudnlhcyU1QyU1Q0Rlc2t0b3AlNUMlNUN0ZXN0JTVDJTVDbmV4dGpzLWZyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2ZvbnQlNUMlNUNnb29nbGUlNUMlNUN0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMmFwcCU1QyU1QyU1QyU1Q2xheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2xha3NoaXRhLnZ5YXMlNUMlNUNEZXNrdG9wJTVDJTVDdGVzdCU1QyU1Q25leHRqcy1mcm9udGVuZCU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrSkFBZ0oiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kYWRwLWZyb250ZW5kLz9mMmYyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiUHJvdmlkZXJzXCJdICovIFwiQzpcXFxcVXNlcnNcXFxcbGFrc2hpdGEudnlhc1xcXFxEZXNrdG9wXFxcXHRlc3RcXFxcbmV4dGpzLWZyb250ZW5kXFxcXGFwcFxcXFxwcm92aWRlcnMudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Capp%5C%5Csynthetic%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Capp%5C%5Csynthetic%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/synthetic/page.tsx */ \"(ssr)/./app/synthetic/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2xha3NoaXRhLnZ5YXMlNUMlNUNEZXNrdG9wJTVDJTVDdGVzdCU1QyU1Q25leHRqcy1mcm9udGVuZCU1QyU1Q2FwcCU1QyU1Q3N5bnRoZXRpYyU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw0SkFBdUgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kYWRwLWZyb250ZW5kLz81OTIxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcbGFrc2hpdGEudnlhc1xcXFxEZXNrdG9wXFxcXHRlc3RcXFxcbmV4dGpzLWZyb250ZW5kXFxcXGFwcFxcXFxzeW50aGV0aWNcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Capp%5C%5Csynthetic%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/providers.tsx":
/*!***************************!*\
  !*** ./app/providers.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _lib_auth_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth-context */ \"(ssr)/./lib/auth-context.tsx\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\n\nfunction Providers({ children }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.QueryClient({\n            defaultOptions: {\n                queries: {\n                    staleTime: 60 * 1000,\n                    retry: 1\n                }\n            }\n        }));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.QueryClientProvider, {\n        client: queryClient,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_auth_context__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n            children: [\n                children,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n                    position: \"top-right\",\n                    toastOptions: {\n                        duration: 5000,\n                        style: {\n                            background: \"#fff\",\n                            color: \"#333\",\n                            border: \"1px solid #e2e8f0\",\n                            borderRadius: \"8px\",\n                            boxShadow: \"0 4px 20px rgba(0,0,0,0.1)\"\n                        },\n                        success: {\n                            iconTheme: {\n                                primary: \"#38a169\",\n                                secondary: \"#fff\"\n                            }\n                        },\n                        error: {\n                            iconTheme: {\n                                primary: \"#e53e3e\",\n                                secondary: \"#fff\"\n                            }\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\providers.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\providers.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\providers.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./app/synthetic/page.tsx":
/*!********************************!*\
  !*** ./app/synthetic/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SyntheticHomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Book,Bot,Brain,ChevronDown,Code,Cpu,Download,FileText,Folder,Gavel,GraduationCap,HelpCircle,Home,Layers,List,Network,Rocket,RotateCcw,Scale,Server,Settings,Share,Tag,Target,TrendingUp,Wand2,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/help-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Book,Bot,Brain,ChevronDown,Code,Cpu,Download,FileText,Folder,Gavel,GraduationCap,HelpCircle,Home,Layers,List,Network,Rocket,RotateCcw,Scale,Server,Settings,Share,Tag,Target,TrendingUp,Wand2,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Book,Bot,Brain,ChevronDown,Code,Cpu,Download,FileText,Folder,Gavel,GraduationCap,HelpCircle,Home,Layers,List,Network,Rocket,RotateCcw,Scale,Server,Settings,Share,Tag,Target,TrendingUp,Wand2,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/book.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Book,Bot,Brain,ChevronDown,Code,Cpu,Download,FileText,Folder,Gavel,GraduationCap,HelpCircle,Home,Layers,List,Network,Rocket,RotateCcw,Scale,Server,Settings,Share,Tag,Target,TrendingUp,Wand2,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/layers.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Book,Bot,Brain,ChevronDown,Code,Cpu,Download,FileText,Folder,Gavel,GraduationCap,HelpCircle,Home,Layers,List,Network,Rocket,RotateCcw,Scale,Server,Settings,Share,Tag,Target,TrendingUp,Wand2,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Book,Bot,Brain,ChevronDown,Code,Cpu,Download,FileText,Folder,Gavel,GraduationCap,HelpCircle,Home,Layers,List,Network,Rocket,RotateCcw,Scale,Server,Settings,Share,Tag,Target,TrendingUp,Wand2,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/scale.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Book,Bot,Brain,ChevronDown,Code,Cpu,Download,FileText,Folder,Gavel,GraduationCap,HelpCircle,Home,Layers,List,Network,Rocket,RotateCcw,Scale,Server,Settings,Share,Tag,Target,TrendingUp,Wand2,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/network.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Book,Bot,Brain,ChevronDown,Code,Cpu,Download,FileText,Folder,Gavel,GraduationCap,HelpCircle,Home,Layers,List,Network,Rocket,RotateCcw,Scale,Server,Settings,Share,Tag,Target,TrendingUp,Wand2,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Book,Bot,Brain,ChevronDown,Code,Cpu,Download,FileText,Folder,Gavel,GraduationCap,HelpCircle,Home,Layers,List,Network,Rocket,RotateCcw,Scale,Server,Settings,Share,Tag,Target,TrendingUp,Wand2,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/gavel.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Book,Bot,Brain,ChevronDown,Code,Cpu,Download,FileText,Folder,Gavel,GraduationCap,HelpCircle,Home,Layers,List,Network,Rocket,RotateCcw,Scale,Server,Settings,Share,Tag,Target,TrendingUp,Wand2,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Book,Bot,Brain,ChevronDown,Code,Cpu,Download,FileText,Folder,Gavel,GraduationCap,HelpCircle,Home,Layers,List,Network,Rocket,RotateCcw,Scale,Server,Settings,Share,Tag,Target,TrendingUp,Wand2,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Book,Bot,Brain,ChevronDown,Code,Cpu,Download,FileText,Folder,Gavel,GraduationCap,HelpCircle,Home,Layers,List,Network,Rocket,RotateCcw,Scale,Server,Settings,Share,Tag,Target,TrendingUp,Wand2,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Book,Bot,Brain,ChevronDown,Code,Cpu,Download,FileText,Folder,Gavel,GraduationCap,HelpCircle,Home,Layers,List,Network,Rocket,RotateCcw,Scale,Server,Settings,Share,Tag,Target,TrendingUp,Wand2,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/folder.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Book,Bot,Brain,ChevronDown,Code,Cpu,Download,FileText,Folder,Gavel,GraduationCap,HelpCircle,Home,Layers,List,Network,Rocket,RotateCcw,Scale,Server,Settings,Share,Tag,Target,TrendingUp,Wand2,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Book,Bot,Brain,ChevronDown,Code,Cpu,Download,FileText,Folder,Gavel,GraduationCap,HelpCircle,Home,Layers,List,Network,Rocket,RotateCcw,Scale,Server,Settings,Share,Tag,Target,TrendingUp,Wand2,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Book,Bot,Brain,ChevronDown,Code,Cpu,Download,FileText,Folder,Gavel,GraduationCap,HelpCircle,Home,Layers,List,Network,Rocket,RotateCcw,Scale,Server,Settings,Share,Tag,Target,TrendingUp,Wand2,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Book,Bot,Brain,ChevronDown,Code,Cpu,Download,FileText,Folder,Gavel,GraduationCap,HelpCircle,Home,Layers,List,Network,Rocket,RotateCcw,Scale,Server,Settings,Share,Tag,Target,TrendingUp,Wand2,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Book,Bot,Brain,ChevronDown,Code,Cpu,Download,FileText,Folder,Gavel,GraduationCap,HelpCircle,Home,Layers,List,Network,Rocket,RotateCcw,Scale,Server,Settings,Share,Tag,Target,TrendingUp,Wand2,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Book,Bot,Brain,ChevronDown,Code,Cpu,Download,FileText,Folder,Gavel,GraduationCap,HelpCircle,Home,Layers,List,Network,Rocket,RotateCcw,Scale,Server,Settings,Share,Tag,Target,TrendingUp,Wand2,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Book,Bot,Brain,ChevronDown,Code,Cpu,Download,FileText,Folder,Gavel,GraduationCap,HelpCircle,Home,Layers,List,Network,Rocket,RotateCcw,Scale,Server,Settings,Share,Tag,Target,TrendingUp,Wand2,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Book,Bot,Brain,ChevronDown,Code,Cpu,Download,FileText,Folder,Gavel,GraduationCap,HelpCircle,Home,Layers,List,Network,Rocket,RotateCcw,Scale,Server,Settings,Share,Tag,Target,TrendingUp,Wand2,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Book,Bot,Brain,ChevronDown,Code,Cpu,Download,FileText,Folder,Gavel,GraduationCap,HelpCircle,Home,Layers,List,Network,Rocket,RotateCcw,Scale,Server,Settings,Share,Tag,Target,TrendingUp,Wand2,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wand-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Book,Bot,Brain,ChevronDown,Code,Cpu,Download,FileText,Folder,Gavel,GraduationCap,HelpCircle,Home,Layers,List,Network,Rocket,RotateCcw,Scale,Server,Settings,Share,Tag,Target,TrendingUp,Wand2,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/share.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Book,Bot,Brain,ChevronDown,Code,Cpu,Download,FileText,Folder,Gavel,GraduationCap,HelpCircle,Home,Layers,List,Network,Rocket,RotateCcw,Scale,Server,Settings,Share,Tag,Target,TrendingUp,Wand2,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Book,Bot,Brain,ChevronDown,Code,Cpu,Download,FileText,Folder,Gavel,GraduationCap,HelpCircle,Home,Layers,List,Network,Rocket,RotateCcw,Scale,Server,Settings,Share,Tag,Target,TrendingUp,Wand2,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Book,Bot,Brain,ChevronDown,Code,Cpu,Download,FileText,Folder,Gavel,GraduationCap,HelpCircle,Home,Layers,List,Network,Rocket,RotateCcw,Scale,Server,Settings,Share,Tag,Target,TrendingUp,Wand2,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Book,Bot,Brain,ChevronDown,Code,Cpu,Download,FileText,Folder,Gavel,GraduationCap,HelpCircle,Home,Layers,List,Network,Rocket,RotateCcw,Scale,Server,Settings,Share,Tag,Target,TrendingUp,Wand2,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Book,Bot,Brain,ChevronDown,Code,Cpu,Download,FileText,Folder,Gavel,GraduationCap,HelpCircle,Home,Layers,List,Network,Rocket,RotateCcw,Scale,Server,Settings,Share,Tag,Target,TrendingUp,Wand2,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction SyntheticHomePage() {\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsVisible(true);\n    }, []);\n    const datasetTypes = [\n        {\n            icon: _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            title: \"Question-Answer Pairs\",\n            description: \"Standard format with questions and corresponding correct answers\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            title: \"Entity Extraction\",\n            description: \"Identify and extract named entities, terms, and key information points\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            title: \"Concept Definitions\",\n            description: \"Explanations and definitions of key terms and concepts\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            title: \"Summarization\",\n            description: \"Condensed versions of longer texts capturing key information\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            title: \"Procedures\",\n            description: \"Step-by-step instructions for completing specific tasks\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            title: \"Comparisons\",\n            description: \"Contrasting different items, concepts, or approaches\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            title: \"Role Relationships\",\n            description: \"Connections and interactions between different entities\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            title: \"Code Examples\",\n            description: \"Examples of code snippets based on the topic from the source\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            title: \"Fact vs Opinion\",\n            description: \"Classification of statements as factual or subjective\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            title: \"Cause-Effect Relationships\",\n            description: \"Identifying connections between causes and their results\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            title: \"Paraphrases\",\n            description: \"Alternative ways to express the same information\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n            title: \"Intent Detection\",\n            description: \"Identifying the purpose or goal behind a piece of text\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n            title: \"Topic Classification\",\n            description: \"Categorizing content according to subject matter\"\n        }\n    ];\n    const models = [\n        {\n            icon: _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n            title: \"OpenAI\",\n            description: \"Advanced GPT models for generating high-quality, contextual question-answer pairs\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n            title: \"Claude\",\n            description: \"Specialized in detailed and nuanced responses for complex domain knowledge\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n            title: \"Gemini\",\n            description: \"Google's multimodal model with strong reasoning and content generation capabilities\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n            title: \"Deepseek r1\",\n            description: \"Run various open-source models locally using Ollama with customizable parameters\"\n        }\n    ];\n    const features = [\n        {\n            icon: _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n            title: \"Source Analysis\",\n            description: \"Upload PDFs, text files, and other documents like web page urls to generate comprehensive datasets\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n            title: \"Customizable Settings\",\n            description: \"Configure number of QA pairs, difficulty levels, and query (optional)\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n            title: \"Export Options\",\n            description: \"Download your generated datasets in multiple formats for training or evaluation\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n            title: \"Model Distillation\",\n            description: \"Create compact datasets for transferring knowledge from large to smaller models\"\n        }\n    ];\n    const steps = [\n        {\n            number: 1,\n            title: \"Select Dataset Types\",\n            description: \"Choose from multiple dataset types based on your specific needs\"\n        },\n        {\n            number: 2,\n            title: \"Upload Sources\",\n            description: \"Add documents or web pages to generate datasets from\"\n        },\n        {\n            number: 3,\n            title: \"Add Keywords (Optional)\",\n            description: \"Provide specific keywords as queries to focus the dataset generation\"\n        },\n        {\n            number: 4,\n            title: \"Select AI Model\",\n            description: \"Choose from OpenAI, Claude, Gemini, or Ollama based on your requirements\"\n        },\n        {\n            number: 5,\n            title: \"Configure Settings\",\n            description: \"Set the number of items to generate and difficulty level\"\n        },\n        {\n            number: 6,\n            title: \"Generate Dataset\",\n            description: \"Create your custom dataset using the selected parameters\"\n        },\n        {\n            number: 7,\n            title: \"Download Results\",\n            description: \"Export your dataset in various formats including JSON, CSV, and TXT\"\n        }\n    ];\n    const distillationTypes = [\n        {\n            icon: _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n            title: \"Data-Free Knowledge Distillation\",\n            description: \"Transfer knowledge from a teacher model to a student model without relying on original training data by generating synthetic data to guide the student's learning\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n            title: \"Response Based Knowledge Distillation\",\n            description: \"Transfers information from the final output layer of the teacher model by training the student model to output logits that match the teacher model's predictions\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            title: \"Feature Based Knowledge Distillation\",\n            description: \"Focuses on information conveyed in intermediate layers where neural networks perform feature extraction, training the student to learn the same features as the teacher\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            title: \"Relation Based Knowledge Distillation\",\n            description: \"Focuses on the relationships between different layers or between feature maps representing the activations at different layers or locations\"\n        }\n    ];\n    const useCases = [\n        {\n            icon: _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n            title: \"AI Model Training\",\n            description: \"Create training datasets to improve AI model performance on domain-specific knowledge\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n            title: \"Performance Evaluation\",\n            description: \"Generate test sets to evaluate how well AI models understand your content\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n            title: \"Educational Resources\",\n            description: \"Create Q&A materials for educational purposes or knowledge assessment\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-gradient-to-br from-primary-500/5 to-secondary-500/5 pointer-events-none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"relative z-50 bg-white/90 backdrop-blur-sm border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/\",\n                                        className: \"flex items-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            src: \"/img/PVlogo-1024x780.png\",\n                                            alt: \"ProcessVenue Logo\",\n                                            width: 40,\n                                            height: 30,\n                                            className: \"h-8 w-auto\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden sm:block\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium text-gray-900\",\n                                                children: \"End to End Synthetic Dataset Solutions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-primary-500 font-semibold\",\n                                                children: \"Human-AI Collaboration • BETA VERSION\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"hidden md:flex items-center space-x-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#top\",\n                                        className: \"text-gray-700 hover:text-primary-500 transition-colors\",\n                                        children: \"Home\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#features\",\n                                        className: \"text-gray-700 hover:text-primary-500 transition-colors\",\n                                        children: \"Features\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#how-it-works\",\n                                        className: \"text-gray-700 hover:text-primary-500 transition-colors\",\n                                        children: \"How It Works\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#dataset-types\",\n                                        className: \"text-gray-700 hover:text-primary-500 transition-colors\",\n                                        children: \"Datasets\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#model-distillation\",\n                                        className: \"text-gray-700 hover:text-primary-500 transition-colors\",\n                                        children: \"Distillation\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#use-cases\",\n                                        className: \"text-gray-700 hover:text-primary-500 transition-colors\",\n                                        children: \"Use Cases\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/\",\n                                className: \"btn btn-outline\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Back to DADP\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"top\",\n                className: \"relative py-20 lg:py-32 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container relative\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `max-w-4xl mx-auto text-center transition-all duration-1000 ${isVisible ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-10\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-5xl lg:text-7xl font-bold text-gray-900 mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gradient\",\n                                    children: \"SynGround\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl lg:text-2xl text-gray-600 mb-8\",\n                                children: \"Accelerate model training with high-quality synthetic data and efficient knowledge transfer\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4 mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg text-primary-600 font-medium\",\n                                        children: \"Generate synthetic datasets from any source\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg text-secondary-600 font-medium\",\n                                        children: \"Train and optimize AI models efficiently\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg text-accent-600 font-medium\",\n                                        children: \"Transfer knowledge between models seamlessly\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/login\",\n                                className: \"btn btn-primary btn-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                        className: \"w-5 h-5 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Get Started\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"#features\",\n                                    className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                        className: \"w-6 h-6 mx-auto animate-bounce\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"features\",\n                className: \"py-20 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl lg:text-4xl font-bold text-center text-gray-900 mb-16\",\n                            children: \"Dataset Generation Features\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                            children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card card-body text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                className: \"w-8 h-8 text-primary-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900 mb-3\",\n                                            children: feature.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: feature.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"how-it-works\",\n                className: \"py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl lg:text-4xl font-bold text-center text-gray-900 mb-16\",\n                            children: \"How Dataset Generation Works\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8\",\n                            children: steps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card card-body text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-primary-500 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold\",\n                                            children: step.number\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-3\",\n                                            children: step.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm\",\n                                            children: step.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"models\",\n                className: \"py-20 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl lg:text-4xl font-bold text-center text-gray-900 mb-16\",\n                            children: \"Models Available\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                            children: models.map((model, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card card-body text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-secondary-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(model.icon, {\n                                                className: \"w-8 h-8 text-secondary-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900 mb-3\",\n                                            children: model.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: model.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                lineNumber: 215,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"dataset-types\",\n                className: \"py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl lg:text-4xl font-bold text-center text-gray-900 mb-16\",\n                            children: \"Types of Datasets\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                            children: datasetTypes.map((type, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card card-body text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-accent-100 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(type.icon, {\n                                                className: \"w-6 h-6 text-accent-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: type.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm\",\n                                            children: type.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                    lineNumber: 236,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                lineNumber: 235,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"model-distillation\",\n                className: \"py-20 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl lg:text-4xl font-bold text-center text-gray-900 mb-8\",\n                            children: \"Model Distillation\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto text-center mb-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-600\",\n                                children: \"Model Distillation is a compression technique where a large Teacher Model transfers its knowledge to a smaller Student Model while keeping performance high. The goal is to make AI models smaller, faster, and more efficient for real-world applications.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-bold text-center text-gray-900 mb-8\",\n                            children: \"Types of Model Distillation\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-xl font-semibold text-gray-900 mb-6\",\n                                    children: \"When no real data available:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 gap-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card card-body text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-warning-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    className: \"w-8 h-8 text-warning-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold text-gray-900 mb-3\",\n                                                children: \"Data-Free Knowledge Distillation\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Transfer knowledge from a teacher model to a student model without relying on original training data by generating synthetic data to guide the student's learning\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-xl font-semibold text-gray-900 mb-6\",\n                                    children: \"When real data is available:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                                    children: distillationTypes.slice(1).map((type, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card card-body text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-info-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(type.icon, {\n                                                        className: \"w-8 h-8 text-info-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-semibold text-gray-900 mb-3\",\n                                                    children: type.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: type.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                    lineNumber: 256,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                lineNumber: 255,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"use-cases\",\n                className: \"py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl lg:text-4xl font-bold text-center text-gray-900 mb-16\",\n                            children: \"Dataset Use Cases\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                            children: useCases.map((useCase, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card card-body text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-success-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(useCase.icon, {\n                                                className: \"w-8 h-8 text-success-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900 mb-3\",\n                                            children: useCase.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: useCase.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                    lineNumber: 300,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                lineNumber: 299,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-gray-900 text-white py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"\\xa9 2025 Process Venue. All rights reserved.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                        lineNumber: 321,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                    lineNumber: 320,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                lineNumber: 319,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                href: \"#top\",\n                className: \"fixed bottom-8 right-8 w-12 h-12 bg-primary-500 text-white rounded-full flex items-center justify-center shadow-lg hover:bg-primary-600 transition-colors z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Book_Bot_Brain_ChevronDown_Code_Cpu_Download_FileText_Folder_Gavel_GraduationCap_HelpCircle_Home_Layers_List_Network_Rocket_RotateCcw_Scale_Server_Settings_Share_Tag_Target_TrendingUp_Wand2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                    className: \"w-5 h-5 rotate-180\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                    lineNumber: 332,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n                lineNumber: 328,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\synthetic\\\\page.tsx\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/synthetic/page.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/api-client.ts":
/*!***************************!*\
  !*** ./lib/api-client.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   api: () => (/* binding */ api),\n/* harmony export */   apiClient: () => (/* binding */ apiClient)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\n// Create axios instance with base configuration\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: \"http://localhost:5000\" || 0,\n    timeout: 30000,\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n});\n// Request interceptor to add auth token\napiClient.interceptors.request.use((config)=>{\n    const token = localStorage.getItem(\"auth_token\");\n    if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Response interceptor to handle common errors\napiClient.interceptors.response.use((response)=>{\n    return response;\n}, (error)=>{\n    if (error.response?.status === 401) {\n        // Unauthorized - clear token and redirect to login\n        localStorage.removeItem(\"auth_token\");\n        if (false) {}\n    }\n    return Promise.reject(error);\n});\n// API helper functions\nconst api = {\n    // Authentication\n    auth: {\n        login: (credentials)=>apiClient.post(\"/auth/login\", credentials),\n        logout: ()=>apiClient.post(\"/auth/logout\"),\n        me: ()=>apiClient.get(\"/auth/me\"),\n        changePassword: (data)=>apiClient.post(\"/auth/change-password\", data)\n    },\n    // User management\n    users: {\n        list: (params)=>apiClient.get(\"/users\", {\n                params\n            }),\n        create: (userData)=>apiClient.post(\"/users\", userData),\n        update: (id, userData)=>apiClient.put(`/users/${id}`, userData),\n        delete: (id)=>apiClient.delete(`/users/${id}`),\n        get: (id)=>apiClient.get(`/users/${id}`)\n    },\n    // File management\n    files: {\n        browse: (path)=>apiClient.get(\"/files/browse\", {\n                params: {\n                    path\n                }\n            }),\n        upload: (formData)=>apiClient.post(\"/files/upload\", formData, {\n                headers: {\n                    \"Content-Type\": \"multipart/form-data\"\n                }\n            }),\n        delete: (path)=>apiClient.delete(\"/files\", {\n                params: {\n                    path\n                }\n            })\n    },\n    // Annotation tasks\n    annotations: {\n        list: (params)=>apiClient.get(\"/annotations\", {\n                params\n            }),\n        get: (id)=>apiClient.get(`/annotations/${id}`),\n        create: (data)=>apiClient.post(\"/annotations\", data),\n        update: (id, data)=>apiClient.put(`/annotations/${id}`, data),\n        save: (id, annotationData)=>apiClient.post(`/annotations/${id}/save`, annotationData),\n        submit: (id)=>apiClient.post(`/annotations/${id}/submit`)\n    },\n    // Audit tasks\n    audits: {\n        list: (params)=>apiClient.get(\"/audits\", {\n                params\n            }),\n        get: (id)=>apiClient.get(`/audits/${id}`),\n        approve: (id, feedback)=>apiClient.post(`/audits/${id}/approve`, {\n                feedback\n            }),\n        reject: (id, feedback)=>apiClient.post(`/audits/${id}/reject`, {\n                feedback\n            })\n    },\n    // Data connectors\n    connectors: {\n        nas: {\n            connect: (config)=>apiClient.post(\"/connectors/nas/connect\", config),\n            disconnect: ()=>apiClient.post(\"/connectors/nas/disconnect\"),\n            status: ()=>apiClient.get(\"/connectors/nas/status\"),\n            browse: (path)=>apiClient.get(\"/connectors/nas/browse\", {\n                    params: {\n                        path\n                    }\n                })\n        },\n        googleDrive: {\n            configure: (config)=>apiClient.post(\"/connectors/google-drive/configure\", config),\n            status: ()=>apiClient.get(\"/connectors/google-drive/status\"),\n            reset: ()=>apiClient.post(\"/connectors/google-drive/reset\")\n        }\n    },\n    // Dashboard\n    dashboard: {\n        stats: ()=>apiClient.get(\"/dashboard/stats\"),\n        userStats: (userId)=>apiClient.get(\"/dashboard/user-stats\", {\n                params: {\n                    user_id: userId\n                }\n            })\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/api-client.ts\n");

/***/ }),

/***/ "(ssr)/./lib/auth-context.tsx":
/*!******************************!*\
  !*** ./lib/auth-context.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _api_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./api-client */ \"(ssr)/./lib/api-client.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check for existing session on mount\n        checkSession();\n    }, []);\n    const checkSession = async ()=>{\n        try {\n            const token = localStorage.getItem(\"auth_token\");\n            if (!token) {\n                setLoading(false);\n                return;\n            }\n            const response = await _api_client__WEBPACK_IMPORTED_MODULE_2__.apiClient.get(\"/auth/me\");\n            if (response.data.success) {\n                setUser(response.data.data.user);\n                setSession(response.data.data);\n            } else {\n                localStorage.removeItem(\"auth_token\");\n            }\n        } catch (error) {\n            console.error(\"Session check failed:\", error);\n            localStorage.removeItem(\"auth_token\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const login = async (username, password)=>{\n        try {\n            setLoading(true);\n            const response = await _api_client__WEBPACK_IMPORTED_MODULE_2__.apiClient.post(\"/auth/login\", {\n                username,\n                password\n            });\n            if (response.data.success) {\n                const sessionData = response.data.data;\n                setUser(sessionData.user);\n                setSession(sessionData);\n                localStorage.setItem(\"auth_token\", sessionData.token);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"Login successful!\");\n                return true;\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(response.data.message || \"Login failed\");\n                return false;\n            }\n        } catch (error) {\n            const message = error.response?.data?.message || \"Login failed\";\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(message);\n            return false;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const logout = ()=>{\n        setUser(null);\n        setSession(null);\n        localStorage.removeItem(\"auth_token\");\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"Logged out successfully\");\n    };\n    const refreshSession = async ()=>{\n        await checkSession();\n    };\n    const value = {\n        user,\n        session,\n        loading,\n        login,\n        logout,\n        refreshSession\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\lib\\\\auth-context.tsx\",\n        lineNumber: 101,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/auth-context.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"dce4b479c1e7\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kYWRwLWZyb250ZW5kLy4vYXBwL2dsb2JhbHMuY3NzP2ZhMjkiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJkY2U0YjQ3OWMxZTdcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./providers */ \"(rsc)/./app/providers.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"DADP - Data Annotation & Delivery Platform\",\n    description: \"Human Augmented AI Agents platform with HITL for precise AI training Datasets & Enterprise Data Management.\",\n    icons: {\n        icon: \"/favicon.png\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"stylesheet\",\n                        href: \"https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\layout.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\layout.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/providers.tsx":
/*!***************************!*\
  !*** ./app/providers.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\test\nextjs-frontend\app\providers.tsx#Providers`);


/***/ }),

/***/ "(rsc)/./app/synthetic/page.tsx":
/*!********************************!*\
  !*** ./app/synthetic/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\test\nextjs-frontend\app\synthetic\page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/@tanstack","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/react-hot-toast","vendor-chunks/get-intrinsic","vendor-chunks/form-data","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/goober","vendor-chunks/@swc","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsynthetic%2Fpage&page=%2Fsynthetic%2Fpage&appPaths=%2Fsynthetic%2Fpage&pagePath=private-next-app-dir%2Fsynthetic%2Fpage.tsx&appDir=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5Ctest%5Cnextjs-frontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5Ctest%5Cnextjs-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();