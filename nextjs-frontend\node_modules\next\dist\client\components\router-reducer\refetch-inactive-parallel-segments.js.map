{"version": 3, "sources": ["../../../../src/client/components/router-reducer/refetch-inactive-parallel-segments.ts"], "names": ["addRefreshMarkerToActiveParallelSegments", "refreshInactiveParallelSegments", "options", "fetchedSegments", "Set", "refreshInactiveParallelSegmentsImpl", "rootTree", "updatedTree", "state", "updatedCache", "includeNextUrl", "canonicalUrl", "parallelRoutes", "refetch<PERSON>ath", "refetch<PERSON><PERSON><PERSON>", "fetchPromises", "has", "add", "fetchPromise", "fetchServerResponse", "URL", "location", "origin", "nextUrl", "buildId", "then", "fetchResponse", "flightData", "flightDataPath", "applyFlightData", "push", "key", "parallelFetchPromise", "Promise", "all", "tree", "path", "segment", "includes", "PAGE_SEGMENT_KEY"], "mappings": ";;;;;;;;;;;;;;;IAiHgBA,wCAAwC;eAAxCA;;IAvFMC,+BAA+B;eAA/BA;;;iCAvBU;qCACI;yBACH;AAqB1B,eAAeA,gCACpBC,OAAwC;IAExC,MAAMC,kBAAkB,IAAIC;IAC5B,MAAMC,oCAAoC;QACxC,GAAGH,OAAO;QACVI,UAAUJ,QAAQK,WAAW;QAC7BJ;IACF;AACF;AAEA,eAAeE,oCAAoC,KAWlD;IAXkD,IAAA,EACjDG,KAAK,EACLD,WAAW,EACXE,YAAY,EACZC,cAAc,EACdP,eAAe,EACfG,WAAWC,WAAW,EACtBI,YAAY,EAIb,GAXkD;IAYjD,MAAM,GAAGC,gBAAgBC,aAAaC,cAAc,GAAGP;IACvD,MAAMQ,gBAAgB,EAAE;IAExB,IACEF,eACAA,gBAAgBF,gBAChBG,kBAAkB,aAClB,4FAA4F;IAC5F,sDAAsD;IACtD,CAACX,gBAAgBa,GAAG,CAACH,cACrB;QACAV,gBAAgBc,GAAG,CAACJ,aAAa,2BAA2B;;QAE5D,wHAAwH;QACxH,kIAAkI;QAClI,MAAMK,eAAeC,IAAAA,wCAAmB,EACtC,IAAIC,IAAIP,aAAaQ,SAASC,MAAM,GACpC,gGAAgG;QAChG,8HAA8H;QAC9H;YAAChB,QAAQ,CAAC,EAAE;YAAEA,QAAQ,CAAC,EAAE;YAAEA,QAAQ,CAAC,EAAE;YAAE;SAAU,EAClDI,iBAAiBF,MAAMe,OAAO,GAAG,MACjCf,MAAMgB,OAAO,EACbC,IAAI,CAAC,CAACC;YACN,MAAMC,aAAaD,aAAa,CAAC,EAAE;YACnC,IAAI,OAAOC,eAAe,UAAU;gBAClC,KAAK,MAAMC,kBAAkBD,WAAY;oBACvC,wFAAwF;oBACxF,4GAA4G;oBAC5G,4EAA4E;oBAC5EE,IAAAA,gCAAe,EAACpB,cAAcA,cAAcmB;gBAC9C;YACF,OAAO;YACL,4GAA4G;YAC5G,+GAA+G;YAC/G,sEAAsE;YACxE;QACF;QAEAb,cAAce,IAAI,CAACZ;IACrB;IAEA,IAAK,MAAMa,OAAOnB,eAAgB;QAChC,MAAMoB,uBAAuB3B,oCAAoC;YAC/DG;YACAD,aAAaK,cAAc,CAACmB,IAAI;YAChCtB;YACAC;YACAP;YACAG;YACAK;QACF;QAEAI,cAAce,IAAI,CAACE;IACrB;IAEA,MAAMC,QAAQC,GAAG,CAACnB;AACpB;AAQO,SAASf,yCACdmC,IAAuB,EACvBC,IAAY;IAEZ,MAAM,CAACC,SAASzB,kBAAkBE,cAAc,GAAGqB;IACnD,oGAAoG;IACpG,IAAIE,QAAQC,QAAQ,CAACC,yBAAgB,KAAKzB,kBAAkB,WAAW;QACrEqB,IAAI,CAAC,EAAE,GAAGC;QACVD,IAAI,CAAC,EAAE,GAAG;IACZ;IAEA,IAAK,MAAMJ,OAAOnB,eAAgB;QAChCZ,yCAAyCY,cAAc,CAACmB,IAAI,EAAEK;IAChE;AACF"}