{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/pages/websocket.ts"], "names": ["getSocketUrl", "source", "eventCallbacks", "addMessageListener", "callback", "push", "sendMessage", "data", "readyState", "OPEN", "send", "reconnections", "connectHMR", "options", "init", "close", "handleOnline", "window", "console", "log", "handleMessage", "event", "msg", "JSON", "parse", "eventCallback", "timer", "handleDisconnect", "onerror", "onclose", "location", "reload", "clearTimeout", "setTimeout", "url", "assetPrefix", "WebSocket", "path", "onopen", "onmessage"], "mappings": "AACA,SAASA,YAAY,QAAQ,qCAAoC;AAEjE,IAAIC;AAIJ,MAAMC,iBAAwC,EAAE;AAEhD,OAAO,SAASC,mBAAmBC,QAAwB;IACzDF,eAAeG,IAAI,CAACD;AACtB;AAEA,OAAO,SAASE,YAAYC,IAAY;IACtC,IAAI,CAACN,UAAUA,OAAOO,UAAU,KAAKP,OAAOQ,IAAI,EAAE;IAClD,OAAOR,OAAOS,IAAI,CAACH;AACrB;AAEA,IAAII,gBAAgB;AAEpB,OAAO,SAASC,WAAWC,OAA8C;IACvE,SAASC;QACP,IAAIb,QAAQA,OAAOc,KAAK;QAExB,SAASC;YACPL,gBAAgB;YAChBM,OAAOC,OAAO,CAACC,GAAG,CAAC;QACrB;QAEA,SAASC,cAAcC,KAA2B;YAChD,sDAAsD;YACtD,MAAMC,MAAwBC,KAAKC,KAAK,CAACH,MAAMd,IAAI;YACnD,KAAK,MAAMkB,iBAAiBvB,eAAgB;gBAC1CuB,cAAcH;YAChB;QACF;QAEA,IAAII;QACJ,SAASC;YACP1B,OAAO2B,OAAO,GAAG;YACjB3B,OAAO4B,OAAO,GAAG;YACjB5B,OAAOc,KAAK;YACZJ;YACA,yGAAyG;YACzG,IAAIA,gBAAgB,IAAI;gBACtBM,OAAOa,QAAQ,CAACC,MAAM;gBACtB;YACF;YAEAC,aAAaN;YACb,4BAA4B;YAC5BA,QAAQO,WAAWnB,MAAMH,gBAAgB,IAAI,OAAO;QACtD;QAEA,MAAMuB,MAAMlC,aAAaa,QAAQsB,WAAW;QAE5ClC,SAAS,IAAIgB,OAAOmB,SAAS,CAAC,AAAC,KAAEF,MAAMrB,QAAQwB,IAAI;QACnDpC,OAAOqC,MAAM,GAAGtB;QAChBf,OAAO2B,OAAO,GAAGD;QACjB1B,OAAO4B,OAAO,GAAGF;QACjB1B,OAAOsC,SAAS,GAAGnB;IACrB;IAEAN;AACF"}