/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/note-ocr/extractor/page";
exports.ids = ["app/note-ocr/extractor/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnote-ocr%2Fextractor%2Fpage&page=%2Fnote-ocr%2Fextractor%2Fpage&appPaths=%2Fnote-ocr%2Fextractor%2Fpage&pagePath=private-next-app-dir%2Fnote-ocr%2Fextractor%2Fpage.tsx&appDir=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5Ctest%5Cnextjs-frontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5Ctest%5Cnextjs-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnote-ocr%2Fextractor%2Fpage&page=%2Fnote-ocr%2Fextractor%2Fpage&appPaths=%2Fnote-ocr%2Fextractor%2Fpage&pagePath=private-next-app-dir%2Fnote-ocr%2Fextractor%2Fpage.tsx&appDir=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5Ctest%5Cnextjs-frontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5Ctest%5Cnextjs-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'note-ocr',\n        {\n        children: [\n        'extractor',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/note-ocr/extractor/page.tsx */ \"(rsc)/./app/note-ocr/extractor/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/note-ocr/extractor/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/note-ocr/extractor/page\",\n        pathname: \"/note-ocr/extractor\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnote-ocr%2Fextractor%2Fpage&page=%2Fnote-ocr%2Fextractor%2Fpage&appPaths=%2Fnote-ocr%2Fextractor%2Fpage&pagePath=private-next-app-dir%2Fnote-ocr%2Fextractor%2Fpage.tsx&appDir=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5Ctest%5Cnextjs-frontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5Ctest%5Cnextjs-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Capp%5C%5Cnote-ocr%5C%5Cextractor%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Capp%5C%5Cnote-ocr%5C%5Cextractor%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/note-ocr/extractor/page.tsx */ \"(ssr)/./app/note-ocr/extractor/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2xha3NoaXRhLnZ5YXMlNUMlNUNEZXNrdG9wJTVDJTVDdGVzdCU1QyU1Q25leHRqcy1mcm9udGVuZCU1QyU1Q2FwcCU1QyU1Q25vdGUtb2NyJTVDJTVDZXh0cmFjdG9yJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDhLQUFpSSIsInNvdXJjZXMiOlsid2VicGFjazovL2RhZHAtZnJvbnRlbmQvPzcxMWUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxsYWtzaGl0YS52eWFzXFxcXERlc2t0b3BcXFxcdGVzdFxcXFxuZXh0anMtZnJvbnRlbmRcXFxcYXBwXFxcXG5vdGUtb2NyXFxcXGV4dHJhY3RvclxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Capp%5C%5Cnote-ocr%5C%5Cextractor%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/providers.tsx */ \"(ssr)/./app/providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2xha3NoaXRhLnZ5YXMlNUMlNUNEZXNrdG9wJTVDJTVDdGVzdCU1QyU1Q25leHRqcy1mcm9udGVuZCU1QyU1Q2FwcCU1QyU1Q3Byb3ZpZGVycy50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJQcm92aWRlcnMlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDbGFrc2hpdGEudnlhcyU1QyU1Q0Rlc2t0b3AlNUMlNUN0ZXN0JTVDJTVDbmV4dGpzLWZyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2ZvbnQlNUMlNUNnb29nbGUlNUMlNUN0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMmFwcCU1QyU1QyU1QyU1Q2xheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2xha3NoaXRhLnZ5YXMlNUMlNUNEZXNrdG9wJTVDJTVDdGVzdCU1QyU1Q25leHRqcy1mcm9udGVuZCU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrSkFBZ0oiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kYWRwLWZyb250ZW5kLz9mMmYyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiUHJvdmlkZXJzXCJdICovIFwiQzpcXFxcVXNlcnNcXFxcbGFrc2hpdGEudnlhc1xcXFxEZXNrdG9wXFxcXHRlc3RcXFxcbmV4dGpzLWZyb250ZW5kXFxcXGFwcFxcXFxwcm92aWRlcnMudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Clakshita.vyas%5C%5CDesktop%5C%5Ctest%5C%5Cnextjs-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/note-ocr/extractor/page.tsx":
/*!*****************************************!*\
  !*** ./app/note-ocr/extractor/page.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ExtractorPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_Download_Edit_FileText_Folder_Home_Loader2_MessageCircle_Send_Upload_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Copy,Download,Edit,FileText,Folder,Home,Loader2,MessageCircle,Send,Upload,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_Download_Edit_FileText_Folder_Home_Loader2_MessageCircle_Send_Upload_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Copy,Download,Edit,FileText,Folder,Home,Loader2,MessageCircle,Send,Upload,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_Download_Edit_FileText_Folder_Home_Loader2_MessageCircle_Send_Upload_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Copy,Download,Edit,FileText,Folder,Home,Loader2,MessageCircle,Send,Upload,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/folder.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_Download_Edit_FileText_Folder_Home_Loader2_MessageCircle_Send_Upload_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Copy,Download,Edit,FileText,Folder,Home,Loader2,MessageCircle,Send,Upload,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_Download_Edit_FileText_Folder_Home_Loader2_MessageCircle_Send_Upload_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Copy,Download,Edit,FileText,Folder,Home,Loader2,MessageCircle,Send,Upload,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wand-2.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_Download_Edit_FileText_Folder_Home_Loader2_MessageCircle_Send_Upload_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Copy,Download,Edit,FileText,Folder,Home,Loader2,MessageCircle,Send,Upload,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_Download_Edit_FileText_Folder_Home_Loader2_MessageCircle_Send_Upload_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Copy,Download,Edit,FileText,Folder,Home,Loader2,MessageCircle,Send,Upload,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_Download_Edit_FileText_Folder_Home_Loader2_MessageCircle_Send_Upload_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Copy,Download,Edit,FileText,Folder,Home,Loader2,MessageCircle,Send,Upload,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_Download_Edit_FileText_Folder_Home_Loader2_MessageCircle_Send_Upload_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Copy,Download,Edit,FileText,Folder,Home,Loader2,MessageCircle,Send,Upload,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_Download_Edit_FileText_Folder_Home_Loader2_MessageCircle_Send_Upload_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Copy,Download,Edit,FileText,Folder,Home,Loader2,MessageCircle,Send,Upload,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_Download_Edit_FileText_Folder_Home_Loader2_MessageCircle_Send_Upload_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Copy,Download,Edit,FileText,Folder,Home,Loader2,MessageCircle,Send,Upload,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_Download_Edit_FileText_Folder_Home_Loader2_MessageCircle_Send_Upload_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Copy,Download,Edit,FileText,Folder,Home,Loader2,MessageCircle,Send,Upload,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_Download_Edit_FileText_Folder_Home_Loader2_MessageCircle_Send_Upload_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Copy,Download,Edit,FileText,Folder,Home,Loader2,MessageCircle,Send,Upload,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_Download_Edit_FileText_Folder_Home_Loader2_MessageCircle_Send_Upload_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Copy,Download,Edit,FileText,Folder,Home,Loader2,MessageCircle,Send,Upload,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_Download_Edit_FileText_Folder_Home_Loader2_MessageCircle_Send_Upload_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Copy,Download,Edit,FileText,Folder,Home,Loader2,MessageCircle,Send,Upload,Wand2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction ExtractorPage() {\n    const [selectedFile, setSelectedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [previewUrl, setPreviewUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [extractedText, setExtractedText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mode, setMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [customPrompt, setCustomPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [chatMessages, setChatMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"1\",\n            type: \"system\",\n            content: 'You can ask questions about what you see in this image. For example: \"What text appears in this image?\" or \"Describe what you see in this image.\"',\n            timestamp: new Date()\n        }\n    ]);\n    const [chatInput, setChatInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isChatProcessing, setIsChatProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showChat, setShowChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const textAreaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleFileSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((file)=>{\n        if (!file.type.startsWith(\"image/\")) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(\"Please select a valid image file\");\n            return;\n        }\n        setSelectedFile(file);\n        const url = URL.createObjectURL(file);\n        setPreviewUrl(url);\n        setExtractedText(\"\");\n        setMode(null);\n        // Reset chat messages to initial state\n        setChatMessages([\n            {\n                id: \"1\",\n                type: \"system\",\n                content: 'You can ask questions about what you see in this image. For example: \"What text appears in this image?\" or \"Describe what you see in this image.\"',\n                timestamp: new Date()\n            }\n        ]);\n    }, []);\n    const handleDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        e.preventDefault();\n        const files = Array.from(e.dataTransfer.files);\n        if (files.length > 0) {\n            handleFileSelect(files[0]);\n        }\n    }, [\n        handleFileSelect\n    ]);\n    const handleDragOver = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        e.preventDefault();\n    }, []);\n    const handleFileInputChange = (e)=>{\n        const files = e.target.files;\n        if (files && files.length > 0) {\n            handleFileSelect(files[0]);\n        }\n    };\n    const extractText = async (prompt)=>{\n        if (!selectedFile) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(\"Please select an image first\");\n            return;\n        }\n        setIsProcessing(true);\n        try {\n            // Mock OCR processing\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            const mockText = prompt ? `Extracted text using custom prompt: \"${prompt}\"\\n\\nSample extracted content from the image...` : \"Sample extracted text from the image:\\n\\nThis is a demonstration of OCR text extraction. In a real implementation, this would contain the actual text extracted from your uploaded image using advanced OCR technology.\";\n            setExtractedText(mockText);\n            setMode(prompt ? \"custom\" : \"standard\");\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(\"Text extracted successfully!\");\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(\"Failed to extract text\");\n        } finally{\n            setIsProcessing(false);\n        }\n    };\n    const handleChatSubmit = async ()=>{\n        if (!chatInput.trim() || !selectedFile) return;\n        const userMessage = {\n            id: Date.now().toString(),\n            type: \"user\",\n            content: chatInput,\n            timestamp: new Date()\n        };\n        setChatMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setChatInput(\"\");\n        setIsChatProcessing(true);\n        try {\n            // Mock AI response\n            await new Promise((resolve)=>setTimeout(resolve, 1500));\n            const assistantMessage = {\n                id: (Date.now() + 1).toString(),\n                type: \"assistant\",\n                content: `Based on the image you uploaded, I can see that this appears to be a document or image with text content. Your question was: \"${userMessage.content}\". This is a mock response - in a real implementation, this would analyze your specific image and provide detailed answers about its contents.`,\n                timestamp: new Date()\n            };\n            setChatMessages((prev)=>[\n                    ...prev,\n                    assistantMessage\n                ]);\n            setMode(\"chat\");\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(\"Failed to process chat message\");\n        } finally{\n            setIsChatProcessing(false);\n        }\n    };\n    const copyToClipboard = ()=>{\n        navigator.clipboard.writeText(extractedText);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(\"Text copied to clipboard!\");\n    };\n    const downloadText = ()=>{\n        const blob = new Blob([\n            extractedText\n        ], {\n            type: \"text/plain\"\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = \"extracted-text.txt\";\n        a.click();\n        URL.revokeObjectURL(url);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(\"Text downloaded!\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Image Extractor\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/note-ocr\",\n                                className: \"flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_Download_Edit_FileText_Folder_Home_Loader2_MessageCircle_Send_Upload_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Back to Dashboard\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mt-1\",\n                        children: \"Extract text from any image with powerful OCR technology\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                lineNumber: 173,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-6 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-header\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold\",\n                                        children: \"Upload Image\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-body\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            onDrop: handleDrop,\n                                            onDragOver: handleDragOver,\n                                            className: \"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-primary-500 transition-colors cursor-pointer\",\n                                            onClick: ()=>fileInputRef.current?.click(),\n                                            children: [\n                                                previewUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative w-full h-48\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                src: previewUrl,\n                                                                alt: \"Preview\",\n                                                                fill: true,\n                                                                className: \"object-contain rounded-lg\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                                lineNumber: 204,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                            lineNumber: 203,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: selectedFile?.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_Download_Edit_FileText_Folder_Home_Loader2_MessageCircle_Send_Upload_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"w-12 h-12 text-gray-400 mx-auto\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                            lineNumber: 215,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"Drag and drop your image here\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                                    lineNumber: 217,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-500 text-sm\",\n                                                                    children: \"or\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                                    lineNumber: 218,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    className: \"btn btn-outline mt-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_Download_Edit_FileText_Folder_Home_Loader2_MessageCircle_Send_Upload_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                                            lineNumber: 223,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Browse Files\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                                    lineNumber: 219,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    ref: fileInputRef,\n                                                    type: \"file\",\n                                                    accept: \"image/*\",\n                                                    onChange: handleFileInputChange,\n                                                    className: \"hidden\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 15\n                                        }, this),\n                                        selectedFile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-6 space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: \"Choose Mode:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>extractText(),\n                                                            disabled: isProcessing,\n                                                            className: \"btn btn-primary disabled:opacity-50\",\n                                                            children: isProcessing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_Download_Edit_FileText_Folder_Home_Loader2_MessageCircle_Send_Upload_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"w-4 h-4 mr-2 animate-spin\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                                        lineNumber: 249,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Processing...\"\n                                                                ]\n                                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_Download_Edit_FileText_Folder_Home_Loader2_MessageCircle_Send_Upload_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"w-4 h-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                                        lineNumber: 254,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Extract with System Prompt\"\n                                                                ]\n                                                            }, void 0, true)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setMode(\"custom\"),\n                                                            disabled: isProcessing,\n                                                            className: \"btn btn-outline disabled:opacity-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_Download_Edit_FileText_Folder_Home_Loader2_MessageCircle_Send_Upload_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                                    lineNumber: 265,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Extract with Custom Prompt\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                            lineNumber: 260,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setMode(\"chat\"),\n                                                            disabled: isProcessing,\n                                                            className: \"btn btn-outline disabled:opacity-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_Download_Edit_FileText_Folder_Home_Loader2_MessageCircle_Send_Upload_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                                    lineNumber: 274,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Chat with Image\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 19\n                                                }, this),\n                                                mode === \"custom\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"Enter custom extraction prompt:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            value: customPrompt,\n                                                            onChange: (e)=>setCustomPrompt(e.target.value),\n                                                            className: \"form-textarea\",\n                                                            rows: 3,\n                                                            placeholder: \"Extract all text from this image carefully...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>extractText(customPrompt),\n                                                            disabled: isProcessing || !customPrompt.trim(),\n                                                            className: \"btn btn-primary disabled:opacity-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_Download_Edit_FileText_Folder_Home_Loader2_MessageCircle_Send_Upload_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                                    lineNumber: 294,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Extract with Custom Prompt\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-header\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_Download_Edit_FileText_Folder_Home_Loader2_MessageCircle_Send_Upload_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-5 h-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Results\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 17\n                                            }, this),\n                                            extractedText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setIsEditing(!isEditing),\n                                                        className: \"btn btn-outline btn-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_Download_Edit_FileText_Folder_Home_Loader2_MessageCircle_Send_Upload_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            isEditing ? \"Cancel\" : \"Edit\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: copyToClipboard,\n                                                        className: \"btn btn-outline btn-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_Download_Edit_FileText_Folder_Home_Loader2_MessageCircle_Send_Upload_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                                lineNumber: 325,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Copy\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: downloadText,\n                                                        className: \"btn btn-outline btn-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_Download_Edit_FileText_Folder_Home_Loader2_MessageCircle_Send_Upload_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                                lineNumber: 332,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Download\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-body\",\n                                    children: !selectedFile ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-12 text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_Download_Edit_FileText_Folder_Home_Loader2_MessageCircle_Send_Upload_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-16 h-16 mx-auto mb-4 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Upload an image to see extracted text here\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 17\n                                    }, this) : mode === \"chat\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-semibold flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_Download_Edit_FileText_Folder_Home_Loader2_MessageCircle_Send_Upload_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                                lineNumber: 349,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Chat with your image\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowChat(!showChat),\n                                                        className: \"btn btn-outline btn-sm\",\n                                                        children: showChat ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_Download_Edit_FileText_Folder_Home_Loader2_MessageCircle_Send_Upload_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                            lineNumber: 356,\n                                                            columnNumber: 35\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_Download_Edit_FileText_Folder_Home_Loader2_MessageCircle_Send_Upload_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                            lineNumber: 356,\n                                                            columnNumber: 71\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                        lineNumber: 352,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 19\n                                            }, this),\n                                            showChat && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-64 overflow-y-auto border border-gray-200 rounded-lg p-4 space-y-3\",\n                                                        children: [\n                                                            chatMessages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: `flex ${message.type === \"user\" ? \"justify-end\" : \"justify-start\"}`,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: `max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${message.type === \"user\" ? \"bg-primary-500 text-white\" : message.type === \"system\" ? \"bg-gray-100 text-gray-700\" : \"bg-gray-200 text-gray-800\"}`,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm\",\n                                                                            children: message.content\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                                            lineNumber: 377,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                                        lineNumber: 368,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, message.id, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                                    lineNumber: 364,\n                                                                    columnNumber: 27\n                                                                }, this)),\n                                                            isChatProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-start\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-200 text-gray-800 px-4 py-2 rounded-lg\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_Download_Edit_FileText_Folder_Home_Loader2_MessageCircle_Send_Upload_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                                className: \"w-4 h-4 animate-spin\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                                                lineNumber: 385,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm\",\n                                                                                children: \"Analyzing image...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                                                lineNumber: 386,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                                        lineNumber: 384,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                                    lineNumber: 383,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                                lineNumber: 382,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                        lineNumber: 362,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: chatInput,\n                                                                onChange: (e)=>setChatInput(e.target.value),\n                                                                onKeyPress: (e)=>e.key === \"Enter\" && handleChatSubmit(),\n                                                                className: \"form-input flex-1\",\n                                                                placeholder: \"Ask about this image...\",\n                                                                disabled: isChatProcessing\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                                lineNumber: 394,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleChatSubmit,\n                                                                disabled: !chatInput.trim() || isChatProcessing,\n                                                                className: \"btn btn-primary disabled:opacity-50\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_Download_Edit_FileText_Folder_Home_Loader2_MessageCircle_Send_Upload_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                                    lineNumber: 408,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                                lineNumber: 403,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 17\n                                    }, this) : extractedText ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-semibold\",\n                                                        children: \"Extracted Text\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    isEditing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setIsEditing(false),\n                                                                className: \"btn btn-outline btn-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_Download_Edit_FileText_Folder_Home_Loader2_MessageCircle_Send_Upload_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"w-4 h-4 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                                        lineNumber: 424,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Cancel\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                                lineNumber: 420,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>{\n                                                                    setIsEditing(false);\n                                                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(\"Changes saved!\");\n                                                                },\n                                                                className: \"btn btn-success btn-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_Download_Edit_FileText_Folder_Home_Loader2_MessageCircle_Send_Upload_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"w-4 h-4 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                                        lineNumber: 434,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Save\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                                lineNumber: 427,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 19\n                                            }, this),\n                                            isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                ref: textAreaRef,\n                                                value: extractedText,\n                                                onChange: (e)=>setExtractedText(e.target.value),\n                                                className: \"form-textarea h-64\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 border border-gray-200 rounded-lg p-4 h-64 overflow-y-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                    className: \"whitespace-pre-wrap text-sm text-gray-800\",\n                                                    children: extractedText\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                    lineNumber: 450,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                lineNumber: 449,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-12 text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_Download_Edit_FileText_Folder_Home_Loader2_MessageCircle_Send_Upload_Wand2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-16 h-16 mx-auto mb-4 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Choose a mode above to process your image\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\note-ocr\\\\extractor\\\\page.tsx\",\n        lineNumber: 171,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/note-ocr/extractor/page.tsx\n");

/***/ }),

/***/ "(ssr)/./app/providers.tsx":
/*!***************************!*\
  !*** ./app/providers.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _lib_auth_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth-context */ \"(ssr)/./lib/auth-context.tsx\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\n\nfunction Providers({ children }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.QueryClient({\n            defaultOptions: {\n                queries: {\n                    staleTime: 60 * 1000,\n                    retry: 1\n                }\n            }\n        }));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.QueryClientProvider, {\n        client: queryClient,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_auth_context__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n            children: [\n                children,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n                    position: \"top-right\",\n                    toastOptions: {\n                        duration: 5000,\n                        style: {\n                            background: \"#fff\",\n                            color: \"#333\",\n                            border: \"1px solid #e2e8f0\",\n                            borderRadius: \"8px\",\n                            boxShadow: \"0 4px 20px rgba(0,0,0,0.1)\"\n                        },\n                        success: {\n                            iconTheme: {\n                                primary: \"#38a169\",\n                                secondary: \"#fff\"\n                            }\n                        },\n                        error: {\n                            iconTheme: {\n                                primary: \"#e53e3e\",\n                                secondary: \"#fff\"\n                            }\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\providers.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\providers.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\providers.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/api-client.ts":
/*!***************************!*\
  !*** ./lib/api-client.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   api: () => (/* binding */ api),\n/* harmony export */   apiClient: () => (/* binding */ apiClient)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\n// Create axios instance with base configuration\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: \"http://localhost:5000\" || 0,\n    timeout: 30000,\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n});\n// Request interceptor to add auth token\napiClient.interceptors.request.use((config)=>{\n    const token = localStorage.getItem(\"auth_token\");\n    if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Response interceptor to handle common errors\napiClient.interceptors.response.use((response)=>{\n    return response;\n}, (error)=>{\n    if (error.response?.status === 401) {\n        // Unauthorized - clear token and redirect to login\n        localStorage.removeItem(\"auth_token\");\n        if (false) {}\n    }\n    return Promise.reject(error);\n});\n// API helper functions\nconst api = {\n    // Authentication\n    auth: {\n        login: (credentials)=>apiClient.post(\"/auth/login\", credentials),\n        logout: ()=>apiClient.post(\"/auth/logout\"),\n        me: ()=>apiClient.get(\"/auth/me\"),\n        changePassword: (data)=>apiClient.post(\"/auth/change-password\", data)\n    },\n    // User management\n    users: {\n        list: (params)=>apiClient.get(\"/users\", {\n                params\n            }),\n        create: (userData)=>apiClient.post(\"/users\", userData),\n        update: (id, userData)=>apiClient.put(`/users/${id}`, userData),\n        delete: (id)=>apiClient.delete(`/users/${id}`),\n        get: (id)=>apiClient.get(`/users/${id}`)\n    },\n    // File management\n    files: {\n        browse: (path)=>apiClient.get(\"/files/browse\", {\n                params: {\n                    path\n                }\n            }),\n        upload: (formData)=>apiClient.post(\"/files/upload\", formData, {\n                headers: {\n                    \"Content-Type\": \"multipart/form-data\"\n                }\n            }),\n        delete: (path)=>apiClient.delete(\"/files\", {\n                params: {\n                    path\n                }\n            })\n    },\n    // Annotation tasks\n    annotations: {\n        list: (params)=>apiClient.get(\"/annotations\", {\n                params\n            }),\n        get: (id)=>apiClient.get(`/annotations/${id}`),\n        create: (data)=>apiClient.post(\"/annotations\", data),\n        update: (id, data)=>apiClient.put(`/annotations/${id}`, data),\n        save: (id, annotationData)=>apiClient.post(`/annotations/${id}/save`, annotationData),\n        submit: (id)=>apiClient.post(`/annotations/${id}/submit`)\n    },\n    // Audit tasks\n    audits: {\n        list: (params)=>apiClient.get(\"/audits\", {\n                params\n            }),\n        get: (id)=>apiClient.get(`/audits/${id}`),\n        approve: (id, feedback)=>apiClient.post(`/audits/${id}/approve`, {\n                feedback\n            }),\n        reject: (id, feedback)=>apiClient.post(`/audits/${id}/reject`, {\n                feedback\n            })\n    },\n    // Data connectors\n    connectors: {\n        nas: {\n            connect: (config)=>apiClient.post(\"/connectors/nas/connect\", config),\n            disconnect: ()=>apiClient.post(\"/connectors/nas/disconnect\"),\n            status: ()=>apiClient.get(\"/connectors/nas/status\"),\n            browse: (path)=>apiClient.get(\"/connectors/nas/browse\", {\n                    params: {\n                        path\n                    }\n                })\n        },\n        googleDrive: {\n            configure: (config)=>apiClient.post(\"/connectors/google-drive/configure\", config),\n            status: ()=>apiClient.get(\"/connectors/google-drive/status\"),\n            reset: ()=>apiClient.post(\"/connectors/google-drive/reset\")\n        }\n    },\n    // Dashboard\n    dashboard: {\n        stats: ()=>apiClient.get(\"/dashboard/stats\"),\n        userStats: (userId)=>apiClient.get(\"/dashboard/user-stats\", {\n                params: {\n                    user_id: userId\n                }\n            })\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/api-client.ts\n");

/***/ }),

/***/ "(ssr)/./lib/auth-context.tsx":
/*!******************************!*\
  !*** ./lib/auth-context.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _api_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./api-client */ \"(ssr)/./lib/api-client.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check for existing session on mount\n        checkSession();\n    }, []);\n    const checkSession = async ()=>{\n        try {\n            const token = localStorage.getItem(\"auth_token\");\n            if (!token) {\n                setLoading(false);\n                return;\n            }\n            const response = await _api_client__WEBPACK_IMPORTED_MODULE_2__.apiClient.get(\"/auth/me\");\n            if (response.data.success) {\n                setUser(response.data.data.user);\n                setSession(response.data.data);\n            } else {\n                localStorage.removeItem(\"auth_token\");\n            }\n        } catch (error) {\n            console.error(\"Session check failed:\", error);\n            localStorage.removeItem(\"auth_token\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const login = async (username, password)=>{\n        try {\n            setLoading(true);\n            const response = await _api_client__WEBPACK_IMPORTED_MODULE_2__.apiClient.post(\"/auth/login\", {\n                username,\n                password\n            });\n            if (response.data.success) {\n                const sessionData = response.data.data;\n                setUser(sessionData.user);\n                setSession(sessionData);\n                localStorage.setItem(\"auth_token\", sessionData.token);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"Login successful!\");\n                return true;\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(response.data.message || \"Login failed\");\n                return false;\n            }\n        } catch (error) {\n            const message = error.response?.data?.message || \"Login failed\";\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(message);\n            return false;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const logout = ()=>{\n        setUser(null);\n        setSession(null);\n        localStorage.removeItem(\"auth_token\");\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"Logged out successfully\");\n    };\n    const refreshSession = async ()=>{\n        await checkSession();\n    };\n    const value = {\n        user,\n        session,\n        loading,\n        login,\n        logout,\n        refreshSession\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\lib\\\\auth-context.tsx\",\n        lineNumber: 101,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/auth-context.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"dce4b479c1e7\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kYWRwLWZyb250ZW5kLy4vYXBwL2dsb2JhbHMuY3NzP2ZhMjkiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJkY2U0YjQ3OWMxZTdcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./providers */ \"(rsc)/./app/providers.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"DADP - Data Annotation & Delivery Platform\",\n    description: \"Human Augmented AI Agents platform with HITL for precise AI training Datasets & Enterprise Data Management.\",\n    icons: {\n        icon: \"/favicon.png\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"stylesheet\",\n                        href: \"https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\layout.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test\\\\nextjs-frontend\\\\app\\\\layout.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/note-ocr/extractor/page.tsx":
/*!*****************************************!*\
  !*** ./app/note-ocr/extractor/page.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\test\nextjs-frontend\app\note-ocr\extractor\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/providers.tsx":
/*!***************************!*\
  !*** ./app/providers.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\test\nextjs-frontend\app\providers.tsx#Providers`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/@tanstack","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/react-hot-toast","vendor-chunks/get-intrinsic","vendor-chunks/form-data","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/goober","vendor-chunks/@swc","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnote-ocr%2Fextractor%2Fpage&page=%2Fnote-ocr%2Fextractor%2Fpage&appPaths=%2Fnote-ocr%2Fextractor%2Fpage&pagePath=private-next-app-dir%2Fnote-ocr%2Fextractor%2Fpage.tsx&appDir=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5Ctest%5Cnextjs-frontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Clakshita.vyas%5CDesktop%5Ctest%5Cnextjs-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();