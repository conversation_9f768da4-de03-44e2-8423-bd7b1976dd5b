{"version": 3, "sources": ["../../../../src/client/components/router-reducer/ppr-navigations.ts"], "names": ["DEFAULT_SEGMENT_KEY", "PAGE_SEGMENT_KEY", "matchSegment", "createRouterCache<PERSON>ey", "updateCacheNodeOnNavigation", "oldCacheNode", "oldRouterState", "newRouterState", "prefetchData", "prefetchHead", "oldRouterStateChildren", "newRouterStateChildren", "prefetchDataChildren", "oldParallelRoutes", "parallelRoutes", "prefetchParallelRoutes", "Map", "patchedRouterStateChildren", "task<PERSON><PERSON><PERSON><PERSON>", "parallelRouteKey", "newRouterStateChild", "oldRouterStateChild", "oldSegmentMapChild", "get", "prefetchDataChild", "newSegmentChild", "newSegmentKeyChild", "oldSegment<PERSON>hild", "undefined", "oldCacheNodeChild", "task<PERSON><PERSON><PERSON>", "spawnPendingTask", "spawnReusedTask", "spawnTaskForMissingData", "set", "newCacheNodeChild", "node", "newSegmentMapChild", "route", "newCacheNode", "lazyData", "rsc", "prefetchRsc", "head", "loading", "lazyDataResolved", "patchRouterStateWithNewChildren", "children", "baseRouterState", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clone", "routerState", "pendingCacheNode", "createPendingCacheNode", "reusedRouterState", "listenForDynamicRequest", "task", "responsePromise", "then", "response", "flightData", "flightDataPath", "segmentPath", "slice", "serverRouterState", "length", "dynamicData", "dynamicHead", "writeDynamicDataIntoPendingTask", "abortTask", "error", "rootTask", "i", "segment", "taskSegment", "finishTaskUsingDynamicDataPayload", "taskNode", "finishPendingCacheNode", "serverChildren", "dynamicDataChildren", "serverRouterStateChild", "dynamicDataChild", "routerStateChildren", "routerStateChild", "segmentChild", "segmentKeyChild", "isLeafSegment", "size", "maybePrefetchRsc", "maybePrefetchLoading", "createDeferredRsc", "cacheNode", "taskState", "serverState", "taskStateChildren", "serverStateChildren", "dataChildren", "taskStateChild", "serverStateChild", "dataChild", "segmentMapChild", "taskSegmentChild", "taskSegmentKeyChild", "cacheNodeChild", "abortPendingCacheNode", "dynamicSegmentData", "isDeferredRsc", "resolve", "values", "reject", "updateCacheNodeOnPopstateRestoration", "newParallelRoutes", "shouldUsePrefetch", "status", "DEFERRED", "Symbol", "value", "tag", "pendingRsc", "Promise", "res", "rej", "fulfilledRsc", "rejectedRsc", "reason"], "mappings": "AAWA,SACEA,mBAAmB,EACnBC,gBAAgB,QACX,8BAA6B;AACpC,SAASC,YAAY,QAAQ,oBAAmB;AAChD,SAASC,oBAAoB,QAAQ,4BAA2B;AAmBhE,yEAAyE;AACzE,gFAAgF;AAChF,gDAAgD;AAChD,EAAE;AACF,8EAA8E;AAC9E,8EAA8E;AAC9E,gFAAgF;AAChF,eAAe;AACf,EAAE;AACF,gFAAgF;AAChF,6EAA6E;AAC7E,kEAAkE;AAClE,EAAE;AACF,gFAAgF;AAChF,mBAAmB;AACnB,EAAE;AACF,wEAAwE;AACxE,gFAAgF;AAChF,uCAAuC;AACvC,EAAE;AACF,+EAA+E;AAC/E,6EAA6E;AAC7E,+DAA+D;AAC/D,EAAE;AACF,+EAA+E;AAC/E,+EAA+E;AAC/E,EAAE;AACF,8EAA8E;AAC9E,qDAAqD;AACrD,OAAO,SAASC,4BACdC,YAAuB,EACvBC,cAAiC,EACjCC,cAAiC,EACjCC,YAA+B,EAC/BC,YAA6B;IAE7B,0DAA0D;IAC1D,MAAMC,yBAAyBJ,cAAc,CAAC,EAAE;IAChD,MAAMK,yBAAyBJ,cAAc,CAAC,EAAE;IAChD,MAAMK,uBAAuBJ,YAAY,CAAC,EAAE;IAE5C,MAAMK,oBAAoBR,aAAaS,cAAc;IAErD,2EAA2E;IAC3E,gBAAgB;IAChB,0EAA0E;IAC1E,0EAA0E;IAC1E,4EAA4E;IAC5E,2EAA2E;IAC3E,0EAA0E;IAC1E,uEAAuE;IACvE,yEAAyE;IACzE,wEAAwE;IACxE,+BAA+B;IAC/B,MAAMC,yBAAyB,IAAIC,IAAIH;IAEvC,4EAA4E;IAC5E,4EAA4E;IAC5E,2EAA2E;IAC3E,6EAA6E;IAC7E,mBAAmB;IACnB,IAAII,6BAEA,CAAC;IACL,IAAIC,eAAe;IACnB,IAAK,IAAIC,oBAAoBR,uBAAwB;QACnD,MAAMS,sBACJT,sBAAsB,CAACQ,iBAAiB;QAC1C,MAAME,sBACJX,sBAAsB,CAACS,iBAAiB;QAC1C,MAAMG,qBAAqBT,kBAAkBU,GAAG,CAACJ;QACjD,MAAMK,oBACJZ,oBAAoB,CAACO,iBAAiB;QAExC,MAAMM,kBAAkBL,mBAAmB,CAAC,EAAE;QAC9C,MAAMM,qBAAqBvB,qBAAqBsB;QAEhD,MAAME,kBACJN,wBAAwBO,YAAYP,mBAAmB,CAAC,EAAE,GAAGO;QAE/D,MAAMC,oBACJP,uBAAuBM,YACnBN,mBAAmBC,GAAG,CAACG,sBACvBE;QAEN,IAAIE;QACJ,IAAIL,oBAAoBxB,kBAAkB;YACxC,wEAAwE;YACxE,YAAY;YACZ6B,YAAYC,iBACVX,qBACAI,sBAAsBI,YAAYJ,oBAAoB,MACtDf;QAEJ,OAAO,IAAIgB,oBAAoBzB,qBAAqB;YAClD,0DAA0D;YAC1D,EAAE;YACF,yEAAyE;YACzE,uEAAuE;YACvE,sEAAsE;YACtE,oEAAoE;YACpE,WAAW;YACX,IAAIqB,wBAAwBO,WAAW;gBACrC,sEAAsE;gBACtE,oEAAoE;gBACpE,mEAAmE;gBACnEE,YAAYE,gBAAgBX;YAC9B,OAAO;gBACL,oEAAoE;gBACpES,YAAYC,iBACVX,qBACAI,sBAAsBI,YAAYJ,oBAAoB,MACtDf;YAEJ;QACF,OAAO,IACLkB,oBAAoBC,aACpB1B,aAAauB,iBAAiBE,kBAC9B;YACA,IACEE,sBAAsBD,aACtBP,wBAAwBO,WACxB;gBACA,qDAAqD;gBACrD,IAAIJ,sBAAsBI,aAAaJ,sBAAsB,MAAM;oBACjE,mCAAmC;oBACnCM,YAAY1B,4BACVyB,mBACAR,qBACAD,qBACAI,mBACAf;gBAEJ,OAAO;oBACL,kEAAkE;oBAClE,iEAAiE;oBACjE,kEAAkE;oBAClE,kEAAkE;oBAClE,4BAA4B;oBAC5BqB,YAAYG,wBAAwBb;gBACtC;YACF,OAAO;gBACL,kEAAkE;gBAClE,oEAAoE;gBACpE,iBAAiB;gBACjBU,YAAYC,iBACVX,qBACAI,sBAAsBI,YAAYJ,oBAAoB,MACtDf;YAEJ;QACF,OAAO;YACL,mDAAmD;YACnDqB,YAAYC,iBACVX,qBACAI,sBAAsBI,YAAYJ,oBAAoB,MACtDf;QAEJ;QAEA,IAAIqB,cAAc,MAAM;YACtB,qEAAqE;YACrE,IAAIZ,iBAAiB,MAAM;gBACzBA,eAAe,IAAIF;YACrB;YACAE,aAAagB,GAAG,CAACf,kBAAkBW;YACnC,MAAMK,oBAAoBL,UAAUM,IAAI;YACxC,IAAID,sBAAsB,MAAM;gBAC9B,MAAME,qBAAsC,IAAIrB,IAAIM;gBACpDe,mBAAmBH,GAAG,CAACR,oBAAoBS;gBAC3CpB,uBAAuBmB,GAAG,CAACf,kBAAkBkB;YAC/C;YAEA,oEAAoE;YACpE,uEAAuE;YACvE,YAAY;YACZpB,0BAA0B,CAACE,iBAAiB,GAAGW,UAAUQ,KAAK;QAChE,OAAO;YACL,mEAAmE;YACnErB,0BAA0B,CAACE,iBAAiB,GAAGC;QACjD;IACF;IAEA,IAAIF,iBAAiB,MAAM;QACzB,6BAA6B;QAC7B,OAAO;IACT;IAEA,MAAMqB,eAA+B;QACnCC,UAAU;QACVC,KAAKpC,aAAaoC,GAAG;QACrB,0EAA0E;QAC1E,qEAAqE;QACrE,2EAA2E;QAC3E,0EAA0E;QAC1E,2EAA2E;QAC3E,qCAAqC;QACrCC,aAAarC,aAAaqC,WAAW;QACrCC,MAAMtC,aAAasC,IAAI;QACvBlC,cAAcJ,aAAaI,YAAY;QACvCmC,SAASvC,aAAauC,OAAO;QAE7B,yEAAyE;QACzE9B,gBAAgBC;QAChB8B,kBAAkB;IACpB;IAEA,OAAO;QACL,kEAAkE;QAClEP,OAAOQ,gCACLvC,gBACAU;QAEFmB,MAAMG;QACNQ,UAAU7B;IACZ;AACF;AAEA,SAAS4B,gCACPE,eAAkC,EAClCC,WAA8D;IAE9D,MAAMC,QAA2B;QAACF,eAAe,CAAC,EAAE;QAAEC;KAAY;IAClE,4EAA4E;IAC5E,2EAA2E;IAC3E,uCAAuC;IACvC,IAAI,KAAKD,iBAAiB;QACxBE,KAAK,CAAC,EAAE,GAAGF,eAAe,CAAC,EAAE;IAC/B;IACA,IAAI,KAAKA,iBAAiB;QACxBE,KAAK,CAAC,EAAE,GAAGF,eAAe,CAAC,EAAE;IAC/B;IACA,IAAI,KAAKA,iBAAiB;QACxBE,KAAK,CAAC,EAAE,GAAGF,eAAe,CAAC,EAAE;IAC/B;IACA,OAAOE;AACT;AAEA,SAASnB,iBACPoB,WAA8B,EAC9B3C,YAAsC,EACtCC,YAA6B;IAE7B,sEAAsE;IACtE,MAAM2C,mBAAmBC,uBACvBF,aACA3C,cACAC;IAEF,OAAO;QACL6B,OAAOa;QACPf,MAAMgB;QACNL,UAAU;IACZ;AACF;AAEA,SAASf,gBAAgBsB,iBAAoC;IAC3D,mEAAmE;IACnE,0DAA0D;IAC1D,OAAO;QACLhB,OAAOgB;QACPlB,MAAM;QACNW,UAAU;IACZ;AACF;AAEA,SAASd,wBAAwBkB,WAA8B;IAC7D,wEAAwE;IACxE,6EAA6E;IAC7E,wDAAwD;IACxD,MAAMC,mBAAmBC,uBAAuBF,aAAa,MAAM;IACnE,OAAO;QACLb,OAAOa;QACPf,MAAMgB;QACNL,UAAU;IACZ;AACF;AAEA,4DAA4D;AAC5D,6EAA6E;AAC7E,4EAA4E;AAC5E,+CAA+C;AAC/C,EAAE;AACF,gFAAgF;AAChF,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,8EAA8E;AAC9E,6EAA6E;AAC7E,2CAA2C;AAC3C,EAAE;AACF,4EAA4E;AAC5E,iEAAiE;AACjE,OAAO,SAASQ,wBACdC,IAAU,EACVC,eAAmD;IAEnDA,gBAAgBC,IAAI,CAClB,CAACC;QACC,MAAMC,aAAaD,QAAQ,CAAC,EAAE;QAC9B,KAAK,MAAME,kBAAkBD,WAAY;YACvC,MAAME,cAAcD,eAAeE,KAAK,CAAC,GAAG,CAAC;YAC7C,MAAMC,oBAAoBH,cAAc,CAACA,eAAeI,MAAM,GAAG,EAAE;YACnE,MAAMC,cAAcL,cAAc,CAACA,eAAeI,MAAM,GAAG,EAAE;YAC7D,MAAME,cAAcN,cAAc,CAACA,eAAeI,MAAM,GAAG,EAAE;YAE7D,IAAI,OAAOH,gBAAgB,UAAU;gBAInC;YACF;YAEAM,gCACEZ,MACAM,aACAE,mBACAE,aACAC;QAEJ;QAEA,wEAAwE;QACxE,qEAAqE;QACrE,6DAA6D;QAC7DE,UAAUb,MAAM;IAClB,GACA,CAACc;QACC,2CAA2C;QAC3CD,UAAUb,MAAMc;IAClB;AAEJ;AAEA,SAASF,gCACPG,QAAc,EACdT,WAA8B,EAC9BE,iBAAoC,EACpCE,WAA8B,EAC9BC,WAA4B;IAE5B,4EAA4E;IAC5E,0EAA0E;IAC1E,qCAAqC;IACrC,EAAE;IACF,8EAA8E;IAC9E,qCAAqC;IACrC,EAAE;IACF,6DAA6D;IAC7D,EAAE;IACF,yEAAyE;IACzE,IAAIX,OAAOe;IACX,IAAK,IAAIC,IAAI,GAAGA,IAAIV,YAAYG,MAAM,EAAEO,KAAK,EAAG;QAC9C,MAAMrD,mBAA2B2C,WAAW,CAACU,EAAE;QAC/C,MAAMC,UAAmBX,WAAW,CAACU,IAAI,EAAE;QAC3C,MAAMtD,eAAesC,KAAKT,QAAQ;QAClC,IAAI7B,iBAAiB,MAAM;YACzB,MAAMY,YAAYZ,aAAaK,GAAG,CAACJ;YACnC,IAAIW,cAAcF,WAAW;gBAC3B,MAAM8C,cAAc5C,UAAUQ,KAAK,CAAC,EAAE;gBACtC,IAAIpC,aAAauE,SAASC,cAAc;oBACtC,mEAAmE;oBACnElB,OAAO1B;oBACP;gBACF;YACF;QACF;QACA,2EAA2E;QAC3E,4EAA4E;QAC5E,wEAAwE;QACxE,8BAA8B;QAC9B;IACF;IAEA6C,kCACEnB,MACAQ,mBACAE,aACAC;AAEJ;AAEA,SAASQ,kCACPnB,IAAU,EACVQ,iBAAoC,EACpCE,WAA8B,EAC9BC,WAA4B;IAE5B,0EAA0E;IAC1E,4CAA4C;IAC5C,MAAMjD,eAAesC,KAAKT,QAAQ;IAClC,MAAM6B,WAAWpB,KAAKpB,IAAI;IAC1B,IAAIlB,iBAAiB,MAAM;QACzB,wEAAwE;QACxE,iEAAiE;QACjE,oBAAoB;QACpB,IAAI0D,aAAa,MAAM;YACrBC,uBACED,UACApB,KAAKlB,KAAK,EACV0B,mBACAE,aACAC;YAEF,uDAAuD;YACvDX,KAAKpB,IAAI,GAAG;QACd;QACA;IACF;IACA,2EAA2E;IAC3E,wDAAwD;IACxD,MAAM0C,iBAAiBd,iBAAiB,CAAC,EAAE;IAC3C,MAAMe,sBAAsBb,WAAW,CAAC,EAAE;IAE1C,IAAK,MAAM/C,oBAAoB6C,kBAAmB;QAChD,MAAMgB,yBACJF,cAAc,CAAC3D,iBAAiB;QAClC,MAAM8D,mBACJF,mBAAmB,CAAC5D,iBAAiB;QAEvC,MAAMW,YAAYZ,aAAaK,GAAG,CAACJ;QACnC,IAAIW,cAAcF,WAAW;YAC3B,MAAM8C,cAAc5C,UAAUQ,KAAK,CAAC,EAAE;YACtC,IACEpC,aAAa8E,sBAAsB,CAAC,EAAE,EAAEN,gBACxCO,qBAAqB,QACrBA,qBAAqBrD,WACrB;gBACA,mEAAmE;gBACnE,OAAO+C,kCACL7C,WACAkD,wBACAC,kBACAd;YAEJ;QACF;IACA,2EAA2E;IAC3E,sEAAsE;IACtE,wEAAwE;IACxE,8BAA8B;IAChC;AACF;AAEA,SAASd,uBACPF,WAA8B,EAC9B3C,YAAsC,EACtCC,YAA6B;IAE7B,MAAMyE,sBAAsB/B,WAAW,CAAC,EAAE;IAC1C,MAAMvC,uBAAuBJ,iBAAiB,OAAOA,YAAY,CAAC,EAAE,GAAG;IAEvE,MAAMM,iBAAiB,IAAIE;IAC3B,IAAK,IAAIG,oBAAoB+D,oBAAqB;QAChD,MAAMC,mBACJD,mBAAmB,CAAC/D,iBAAiB;QACvC,MAAMK,oBACJZ,yBAAyB,OACrBA,oBAAoB,CAACO,iBAAiB,GACtC;QAEN,MAAMiE,eAAeD,gBAAgB,CAAC,EAAE;QACxC,MAAME,kBAAkBlF,qBAAqBiF;QAE7C,MAAMjD,oBAAoBkB,uBACxB8B,kBACA3D,sBAAsBI,YAAY,OAAOJ,mBACzCf;QAGF,MAAM4B,qBAAsC,IAAIrB;QAChDqB,mBAAmBH,GAAG,CAACmD,iBAAiBlD;QACxCrB,eAAeoB,GAAG,CAACf,kBAAkBkB;IACvC;IAEA,4EAA4E;IAC5E,mEAAmE;IACnE,MAAMiD,gBAAgBxE,eAAeyE,IAAI,KAAK;IAE9C,MAAMC,mBAAmBhF,iBAAiB,OAAOA,YAAY,CAAC,EAAE,GAAG;IACnE,MAAMiF,uBAAuBjF,iBAAiB,OAAOA,YAAY,CAAC,EAAE,GAAG;IACvE,OAAO;QACLgC,UAAU;QACV1B,gBAAgBA;QAEhB4B,aAAa8C,qBAAqB5D,YAAY4D,mBAAmB;QACjE/E,cAAc6E,gBAAgB7E,eAAe;QAC7CmC,SAAS6C,yBAAyB7D,YAAY6D,uBAAuB;QAErE,qEAAqE;QACrE,wCAAwC;QACxChD,KAAKiD;QACL/C,MAAM2C,gBAAgBI,sBAAsB;QAC5C7C,kBAAkB;IACpB;AACF;AAEA,SAASgC,uBACPc,SAAoB,EACpBC,SAA4B,EAC5BC,WAA8B,EAC9B3B,WAA8B,EAC9BC,WAA4B;IAE5B,8EAA8E;IAC9E,8EAA8E;IAC9E,4EAA4E;IAC5E,8EAA8E;IAC9E,8DAA8D;IAC9D,6BAA6B;IAC7B,EAAE;IACF,qEAAqE;IACrE,8EAA8E;IAC9E,gEAAgE;IAChE,MAAM2B,oBAAoBF,SAAS,CAAC,EAAE;IACtC,MAAMG,sBAAsBF,WAAW,CAAC,EAAE;IAC1C,MAAMG,eAAe9B,WAAW,CAAC,EAAE;IAEnC,8EAA8E;IAC9E,6EAA6E;IAC7E,uCAAuC;IACvC,MAAMpD,iBAAiB6E,UAAU7E,cAAc;IAC/C,IAAK,IAAIK,oBAAoB2E,kBAAmB;QAC9C,MAAMG,iBACJH,iBAAiB,CAAC3E,iBAAiB;QACrC,MAAM+E,mBACJH,mBAAmB,CAAC5E,iBAAiB;QACvC,MAAMgF,YACJH,YAAY,CAAC7E,iBAAiB;QAEhC,MAAMiF,kBAAkBtF,eAAeS,GAAG,CAACJ;QAC3C,MAAMkF,mBAAmBJ,cAAc,CAAC,EAAE;QAC1C,MAAMK,sBAAsBnG,qBAAqBkG;QAEjD,MAAME,iBACJH,oBAAoBxE,YAChBwE,gBAAgB7E,GAAG,CAAC+E,uBACpB1E;QAEN,IAAI2E,mBAAmB3E,WAAW;YAChC,IACEsE,qBAAqBtE,aACrB1B,aAAamG,kBAAkBH,gBAAgB,CAAC,EAAE,GAClD;gBACA,IAAIC,cAAcvE,aAAauE,cAAc,MAAM;oBACjD,+DAA+D;oBAC/DtB,uBACE0B,gBACAN,gBACAC,kBACAC,WACAhC;gBAEJ,OAAO;oBACL,kEAAkE;oBAClE,oEAAoE;oBACpE,sEAAsE;oBACtE,+CAA+C;oBAC/CqC,sBAAsBP,gBAAgBM,gBAAgB;gBACxD;YACF,OAAO;gBACL,kEAAkE;gBAClE,uBAAuB;gBACvBC,sBAAsBP,gBAAgBM,gBAAgB;YACxD;QACF,OAAO;QACL,wEAAwE;QACxE,gEAAgE;QAChE,iEAAiE;QACjE,wDAAwD;QAC1D;IACF;IAEA,2EAA2E;IAC3E,qBAAqB;IACrB,MAAM9D,MAAMkD,UAAUlD,GAAG;IACzB,MAAMgE,qBAAqBvC,WAAW,CAAC,EAAE;IACzC,IAAIzB,QAAQ,MAAM;QAChB,oEAAoE;QACpE,qEAAqE;QACrEkD,UAAUlD,GAAG,GAAGgE;IAClB,OAAO,IAAIC,cAAcjE,MAAM;QAC7B,0EAA0E;QAC1E,sEAAsE;QACtE,sEAAsE;QACtEA,IAAIkE,OAAO,CAACF;IACd,OAAO;IACL,uEAAuE;IACvE,sEAAsE;IACxE;IAEA,8EAA8E;IAC9E,yEAAyE;IACzE,cAAc;IACd,MAAM9D,OAAOgD,UAAUhD,IAAI;IAC3B,IAAI+D,cAAc/D,OAAO;QACvBA,KAAKgE,OAAO,CAACxC;IACf;AACF;AAEA,OAAO,SAASE,UAAUb,IAAU,EAAEc,KAAU;IAC9C,MAAMqB,YAAYnC,KAAKpB,IAAI;IAC3B,IAAIuD,cAAc,MAAM;QACtB,+CAA+C;QAC/C;IACF;IAEA,MAAMzE,eAAesC,KAAKT,QAAQ;IAClC,IAAI7B,iBAAiB,MAAM;QACzB,kEAAkE;QAClE,aAAa;QACbsF,sBAAsBhD,KAAKlB,KAAK,EAAEqD,WAAWrB;IAC/C,OAAO;QACL,sEAAsE;QACtE,2EAA2E;QAC3E,6BAA6B;QAC7B,KAAK,MAAMxC,aAAaZ,aAAa0F,MAAM,GAAI;YAC7CvC,UAAUvC,WAAWwC;QACvB;IACF;IAEA,uDAAuD;IACvDd,KAAKpB,IAAI,GAAG;AACd;AAEA,SAASoE,sBACPrD,WAA8B,EAC9BwC,SAAoB,EACpBrB,KAAU;IAEV,6EAA6E;IAC7E,yCAAyC;IACzC,EAAE;IACF,6DAA6D;IAC7D,MAAMY,sBAAsB/B,WAAW,CAAC,EAAE;IAC1C,MAAMrC,iBAAiB6E,UAAU7E,cAAc;IAC/C,IAAK,IAAIK,oBAAoB+D,oBAAqB;QAChD,MAAMC,mBACJD,mBAAmB,CAAC/D,iBAAiB;QACvC,MAAMiF,kBAAkBtF,eAAeS,GAAG,CAACJ;QAC3C,IAAIiF,oBAAoBxE,WAAW;YAGjC;QACF;QACA,MAAMwD,eAAeD,gBAAgB,CAAC,EAAE;QACxC,MAAME,kBAAkBlF,qBAAqBiF;QAC7C,MAAMmB,iBAAiBH,gBAAgB7E,GAAG,CAAC8D;QAC3C,IAAIkB,mBAAmB3E,WAAW;YAChC4E,sBAAsBrB,kBAAkBoB,gBAAgBjC;QAC1D,OAAO;QACL,wEAAwE;QACxE,wDAAwD;QAC1D;IACF;IACA,MAAM7B,MAAMkD,UAAUlD,GAAG;IACzB,IAAIiE,cAAcjE,MAAM;QACtB,IAAI6B,UAAU,MAAM;YAClB,gDAAgD;YAChD7B,IAAIkE,OAAO,CAAC;QACd,OAAO;YACL,+CAA+C;YAC/ClE,IAAIoE,MAAM,CAACvC;QACb;IACF;IAEA,8EAA8E;IAC9E,4EAA4E;IAC5E,2EAA2E;IAC3E,6DAA6D;IAC7D,MAAM3B,OAAOgD,UAAUhD,IAAI;IAC3B,IAAI+D,cAAc/D,OAAO;QACvBA,KAAKgE,OAAO,CAAC;IACf;AACF;AAEA,OAAO,SAASG,qCACdzG,YAAuB,EACvB8C,WAA8B;IAE9B,2EAA2E;IAC3E,4EAA4E;IAC5E,4EAA4E;IAC5E,4EAA4E;IAC5E,0CAA0C;IAC1C,EAAE;IACF,6EAA6E;IAC7E,8EAA8E;IAC9E,wDAAwD;IAExD,MAAM+B,sBAAsB/B,WAAW,CAAC,EAAE;IAC1C,MAAMtC,oBAAoBR,aAAaS,cAAc;IACrD,MAAMiG,oBAAoB,IAAI/F,IAAIH;IAClC,IAAK,IAAIM,oBAAoB+D,oBAAqB;QAChD,MAAMC,mBACJD,mBAAmB,CAAC/D,iBAAiB;QACvC,MAAMiE,eAAeD,gBAAgB,CAAC,EAAE;QACxC,MAAME,kBAAkBlF,qBAAqBiF;QAC7C,MAAM9D,qBAAqBT,kBAAkBU,GAAG,CAACJ;QACjD,IAAIG,uBAAuBM,WAAW;YACpC,MAAMC,oBAAoBP,mBAAmBC,GAAG,CAAC8D;YACjD,IAAIxD,sBAAsBD,WAAW;gBACnC,MAAMO,oBAAoB2E,qCACxBjF,mBACAsD;gBAEF,MAAM9C,qBAAqB,IAAIrB,IAAIM;gBACnCe,mBAAmBH,GAAG,CAACmD,iBAAiBlD;gBACxC4E,kBAAkB7E,GAAG,CAACf,kBAAkBkB;YAC1C;QACF;IACF;IAEA,kEAAkE;IAClE,EAAE;IACF,0EAA0E;IAC1E,4EAA4E;IAC5E,2EAA2E;IAC3E,8EAA8E;IAC9E,6EAA6E;IAC7E,sBAAsB;IACtB,MAAMI,MAAMpC,aAAaoC,GAAG;IAC5B,MAAMuE,oBAAoBN,cAAcjE,QAAQA,IAAIwE,MAAM,KAAK;IAE/D,OAAO;QACLzE,UAAU;QACVC;QACAE,MAAMtC,aAAasC,IAAI;QAEvBlC,cAAcuG,oBAAoB3G,aAAaI,YAAY,GAAG;QAC9DiC,aAAasE,oBAAoB3G,aAAaqC,WAAW,GAAG;QAC5DE,SAASoE,oBAAoB3G,aAAauC,OAAO,GAAG;QAEpD,kDAAkD;QAClD9B,gBAAgBiG;QAChBlE,kBAAkB;IACpB;AACF;AAEA,MAAMqE,WAAWC;AA8BjB,8EAA8E;AAC9E,gFAAgF;AAChF,8EAA8E;AAC9E,mEAAmE;AACnE,SAAST,cAAcU,KAAU;IAC/B,OAAOA,SAASA,MAAMC,GAAG,KAAKH;AAChC;AAEA,SAASxB;IACP,IAAIiB;IACJ,IAAIE;IACJ,MAAMS,aAAa,IAAIC,QAAyB,CAACC,KAAKC;QACpDd,UAAUa;QACVX,SAASY;IACX;IACAH,WAAWL,MAAM,GAAG;IACpBK,WAAWX,OAAO,GAAG,CAACS;QACpB,IAAIE,WAAWL,MAAM,KAAK,WAAW;YACnC,MAAMS,eAAqCJ;YAC3CI,aAAaT,MAAM,GAAG;YACtBS,aAAaN,KAAK,GAAGA;YACrBT,QAAQS;QACV;IACF;IACAE,WAAWT,MAAM,GAAG,CAACvC;QACnB,IAAIgD,WAAWL,MAAM,KAAK,WAAW;YACnC,MAAMU,cAAmCL;YACzCK,YAAYV,MAAM,GAAG;YACrBU,YAAYC,MAAM,GAAGtD;YACrBuC,OAAOvC;QACT;IACF;IACAgD,WAAWD,GAAG,GAAGH;IACjB,OAAOI;AACT"}