'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import {
  Home,
  Download,
  TrendingUp,
  FileText,
  Database,
  Menu,
  X
} from 'lucide-react';

export default function DocumindHomePage() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [typingText, setTypingText] = useState('');
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);

  const typingMessages = [
    'Human-AI Collaboration',
    'Smart Data Processing',
    'Seamless Integration',
    'Intelligent Automation'
  ];

  useEffect(() => {
    const message = typingMessages[currentMessageIndex];
    let currentIndex = 0;

    const typingInterval = setInterval(() => {
      if (currentIndex <= message.length) {
        setTypingText(message.slice(0, currentIndex));
        currentIndex++;
      } else {
        clearInterval(typingInterval);
        setTimeout(() => {
          setCurrentMessageIndex((prev) => (prev + 1) % typingMessages.length);
        }, 2000);
      }
    }, 100);

    return () => clearInterval(typingInterval);
  }, [currentMessageIndex]);

  const features = [
    {
      icon: Download,
      title: 'Multiple Data Sources',
      description: 'Seamless integration of platforms like Telegram, Google Drive, and more.'
    },
    {
      icon: TrendingUp,
      title: 'Detailed Analytics',
      description: 'Gain insights from your documents with comprehensive analytics and reporting tools.'
    },
    {
      icon: FileText,
      title: 'Multi-format Support',
      description: 'Process diverse documents with intelligent format detection.'
    }
  ];

  const steps = [
    {
      number: 1,
      title: 'Fetch Documents',
      description: 'Connect and extract data from various platforms including Telegram, Google Drive, and more with seamless integration.'
    },
    {
      number: 2,
      title: 'Process in Secure Environment',
      description: 'Select processing power (Standard, Enhanced, or Premium) based on document quality and complexity.'
    },
    {
      number: 3,
      title: 'Review & Export',
      description: 'Inspect processed documents, review extracted data, make corrections, and export it to your desired database.'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-900 text-white relative overflow-hidden">
      {/* Background Effects */}
      <div className="fixed inset-0 bg-gradient-to-br from-gray-900 via-blue-900/20 to-purple-900/20 pointer-events-none"></div>
      <div className="fixed inset-0 bg-[url('/pattern.png')] opacity-5 pointer-events-none"></div>

      {/* Floating Shapes */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 bg-blue-500/10 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-purple-500/10 rounded-full blur-xl animate-pulse delay-1000"></div>
        <div className="absolute bottom-20 left-20 w-40 h-40 bg-cyan-500/10 rounded-full blur-xl animate-pulse delay-2000"></div>
        <div className="absolute bottom-40 right-10 w-20 h-20 bg-pink-500/10 rounded-full blur-xl animate-pulse delay-500"></div>
      </div>

      {/* Header */}
      <header className="relative z-50 bg-gray-900/80 backdrop-blur-sm border-b border-gray-800">
        <div className="container">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link href="/" className="flex items-center">
                <Image
                  src="/img/PVlogo-1024x780.png"
                  alt="Documind-o Logo"
                  width={40}
                  height={30}
                  className="h-8 w-auto"
                />
              </Link>
              <div className="hidden sm:block">
                <div className="text-sm font-medium text-white">
                  End to End Data Solutions
                </div>
                <div className="text-xs text-blue-400 font-semibold">
                  Human-AI Collaboration • Beta Version
                </div>
              </div>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex items-center space-x-8">
              <a href="#features" className="text-gray-300 hover:text-white transition-colors">
                Features
              </a>
              <a href="#how-it-works" className="text-gray-300 hover:text-white transition-colors">
                How It Works
              </a>
            </nav>

            <div className="flex items-center space-x-4">
              <Link href="/" className="btn btn-outline text-white border-white hover:bg-white hover:text-gray-900">
                <Home className="w-4 h-4 mr-2" />
                Back to Home
              </Link>

              {/* Mobile Menu Button */}
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="md:hidden p-2 text-gray-300 hover:text-white"
              >
                {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
              </button>
            </div>
          </div>

          {/* Mobile Menu */}
          {isMenuOpen && (
            <div className="md:hidden py-4 border-t border-gray-800">
              <div className="flex flex-col space-y-2">
                <a
                  href="#features"
                  className="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800 rounded transition-colors"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Features
                </a>
                <a
                  href="#how-it-works"
                  className="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800 rounded transition-colors"
                  onClick={() => setIsMenuOpen(false)}
                >
                  How It Works
                </a>
              </div>
            </div>
          )}
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="container relative">
          <div className="max-w-6xl mx-auto text-center">
            <h1 className="text-5xl lg:text-7xl font-bold mb-6">
              Documind-o<br />
              <span className="text-gradient bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                HAI
              </span>{' '}
              <span className="text-4xl lg:text-5xl">AGENT</span>
            </h1>

            <div className="mb-8 h-8">
              <span className="text-xl lg:text-2xl text-blue-400 font-medium">
                {typingText}
                <span className="animate-pulse">|</span>
              </span>
            </div>

            <h2 className="text-2xl lg:text-3xl text-gray-300 mb-12">
              From Source to Solution → Smart Data Validation and Delivery
            </h2>

            {/* Process Steps */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
              <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 border border-gray-700">
                <h3 className="text-xl font-semibold text-blue-400 mb-2">Data Fetching</h3>
                <p className="text-gray-300">Choose Data Source</p>
              </div>
              <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 border border-gray-700">
                <h3 className="text-xl font-semibold text-purple-400 mb-2">Data Processing</h3>
                <p className="text-gray-300">
                  AI → Time Saving<br />
                  Human → Precision
                </p>
              </div>
              <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 border border-gray-700">
                <h3 className="text-xl font-semibold text-cyan-400 mb-2">Data Delivery</h3>
                <p className="text-gray-300">Save to Desired Database</p>
              </div>
            </div>

            {/* Typing Animation */}
            <div className="mb-12">
              <div className="text-lg text-gray-400 italic">
                "Seamlessly fetch data from anywhere, store everywhere."
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/login?source=data_fetching&role=admin"
                className="btn btn-primary bg-blue-600 hover:bg-blue-700 border-blue-600"
              >
                <Download className="w-5 h-5 mr-2" />
                Data Fetching
              </Link>
              <Link
                href="/login?source=data_processing&role=annotator"
                className="btn btn-primary bg-purple-600 hover:bg-purple-700 border-purple-600"
              >
                <FileText className="w-5 h-5 mr-2" />
                Data Processing
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="relative py-20 bg-gray-800/30">
        <div className="container">
          <h2 className="text-3xl lg:text-4xl font-bold text-center text-white mb-16">
            Features
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div
                key={index}
                className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-8 border border-gray-700 hover:border-gray-600 transition-all duration-300 text-center"
              >
                <div className="w-16 h-16 bg-blue-600/20 rounded-full flex items-center justify-center mx-auto mb-6">
                  <feature.icon className="w-8 h-8 text-blue-400" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-4">{feature.title}</h3>
                <p className="text-gray-300">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section id="how-it-works" className="relative py-20">
        <div className="container">
          <h2 className="text-3xl lg:text-4xl font-bold text-center text-white mb-16">
            How It Works
          </h2>
          <div className="max-w-4xl mx-auto space-y-8">
            {steps.map((step, index) => (
              <div
                key={index}
                className="flex items-start space-x-6 bg-gray-800/30 backdrop-blur-sm rounded-lg p-8 border border-gray-700"
              >
                <div className="flex-shrink-0 w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-bold text-xl">
                  {step.number}
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-white mb-3">{step.title}</h3>
                  <p className="text-gray-300">{step.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="relative bg-gray-900/80 border-t border-gray-800 py-8">
        <div className="container text-center">
          <p className="text-gray-400">
            Documind-o • HAI Agent • All Rights Reserved • © 2025
          </p>
        </div>
      </footer>
    </div>
  );
}
