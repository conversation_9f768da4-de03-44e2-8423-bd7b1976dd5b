{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/server/middleware-turbopack.ts"], "names": ["badRequest", "findSourcePackage", "getOriginalCodeFrame", "internalServerError", "json", "noContent", "fs", "constants", "FS", "launchEditor", "currentSourcesByFile", "Map", "batchedTraceSource", "project", "frame", "file", "decodeURIComponent", "undefined", "sourceFrame", "traceSource", "source", "includes", "isInternal", "sourcePromise", "get", "getSourceForAsset", "set", "setTimeout", "delete", "lineNumber", "line", "column", "methodName", "arguments", "createOriginalStackFrame", "traced", "sourcePackage", "originalStackFrame", "originalCodeFrame", "getOverlayMiddleware", "req", "res", "pathname", "searchParams", "URL", "url", "parseInt", "isServer", "e", "message", "statusCode", "end", "fileExists", "access", "F_OK", "then", "err", "console", "log"], "mappings": "AACA,SACEA,UAAU,EACVC,iBAAiB,EACjBC,oBAAoB,EACpBC,mBAAmB,EACnBC,IAAI,EACJC,SAAS,QAEJ,WAAU;AAEjB,OAAOC,MAAMC,aAAaC,EAAE,QAAQ,cAAa;AACjD,SAASC,YAAY,QAAQ,mCAAkC;AAI/D,MAAMC,uBAA4D,IAAIC;AACtE,OAAO,eAAeC,mBACpBC,OAAgB,EAChBC,KAA0B;IAE1B,MAAMC,OAAOD,MAAMC,IAAI,GAAGC,mBAAmBF,MAAMC,IAAI,IAAIE;IAC3D,IAAI,CAACF,MAAM;IAEX,MAAMG,cAAc,MAAML,QAAQM,WAAW,CAACL;IAC9C,IAAI,CAACI,aAAa;IAElB,IAAIE,SAAS;IACb,8FAA8F;IAC9F,IACEF,YAAYH,IAAI,IAChB,CAAEG,CAAAA,YAAYH,IAAI,CAACM,QAAQ,CAAC,mBAAmBH,YAAYI,UAAU,AAAD,GACpE;QACA,IAAIC,gBAAgBb,qBAAqBc,GAAG,CAACN,YAAYH,IAAI;QAC7D,IAAI,CAACQ,eAAe;YAClBA,gBAAgBV,QAAQY,iBAAiB,CAACP,YAAYH,IAAI;YAC1DL,qBAAqBgB,GAAG,CAACR,YAAYH,IAAI,EAAEQ;YAC3CI,WAAW;gBACT,sEAAsE;gBACtE,0BAA0B;gBAC1BjB,qBAAqBkB,MAAM,CAACV,YAAYH,IAAI;YAC9C,GAAG;QACL;QAEAK,SAAS,MAAMG;IACjB;QAKgBL,mBACJA,qBACIA,yBAAAA;IALhB,OAAO;QACLJ,OAAO;YACLC,MAAMG,YAAYH,IAAI;YACtBc,YAAYX,CAAAA,oBAAAA,YAAYY,IAAI,YAAhBZ,oBAAoB;YAChCa,QAAQb,CAAAA,sBAAAA,YAAYa,MAAM,YAAlBb,sBAAsB;YAC9Bc,YAAYd,CAAAA,OAAAA,CAAAA,0BAAAA,YAAYc,UAAU,YAAtBd,0BAA0BJ,MAAMkB,UAAU,YAA1Cd,OAA8C;YAC1De,WAAW,EAAE;QACf;QACAb;IACF;AACF;AAEA,OAAO,eAAec,yBACpBrB,OAAgB,EAChBC,KAA0B;IAE1B,MAAMqB,SAAS,MAAMvB,mBAAmBC,SAASC;IACjD,IAAI,CAACqB,QAAQ;QACX,MAAMC,gBAAgBnC,kBAAkBa;QACxC,IAAIsB,eAAe,OAAO;YAAEA;QAAc;QAC1C,OAAO;IACT;IAEA,OAAO;QACLC,oBAAoBF,OAAOrB,KAAK;QAChCwB,mBAAmBpC,qBAAqBiC,OAAOrB,KAAK,EAAEqB,OAAOf,MAAM;QACnEgB,eAAenC,kBAAkBkC,OAAOrB,KAAK;IAC/C;AACF;AAEA,OAAO,SAASyB,qBAAqB1B,OAAgB;IACnD,OAAO,eAAgB2B,GAAoB,EAAEC,GAAmB;QAC9D,MAAM,EAAEC,QAAQ,EAAEC,YAAY,EAAE,GAAG,IAAIC,IAAIJ,IAAIK,GAAG,EAAG;YAIvCF,mBACGA,oBACEA;QAJnB,MAAM7B,QAAQ;YACZC,MAAM4B,aAAanB,GAAG,CAAC;YACvBQ,YAAYW,CAAAA,oBAAAA,aAAanB,GAAG,CAAC,yBAAjBmB,oBAAkC;YAC9Cb,MAAMgB,SAASH,CAAAA,qBAAAA,aAAanB,GAAG,CAAC,yBAAjBmB,qBAAkC,KAAK,OAAO;YAC7DZ,QAAQe,SAASH,CAAAA,qBAAAA,aAAanB,GAAG,CAAC,qBAAjBmB,qBAA8B,KAAK,OAAO;YAC3DI,UAAUJ,aAAanB,GAAG,CAAC,gBAAgB;QAC7C;QAEA,IAAIkB,aAAa,kCAAkC;YACjD,IAAIL;YACJ,IAAI;gBACFA,qBAAqB,MAAMH,yBAAyBrB,SAASC;YAC/D,EAAE,OAAOkC,GAAQ;gBACf,OAAO7C,oBAAoBsC,KAAKO,EAAEC,OAAO;YAC3C;YAEA,IAAI,CAACZ,oBAAoB;gBACvBI,IAAIS,UAAU,GAAG;gBACjB,OAAOT,IAAIU,GAAG,CAAC;YACjB;YAEA,OAAO/C,KAAKqC,KAAKJ;QACnB,OAAO,IAAIK,aAAa,2BAA2B;YACjD,IAAI,CAAC5B,MAAMC,IAAI,EAAE,OAAOf,WAAWyC;YAEnC,MAAMW,aAAa,MAAM9C,GAAG+C,MAAM,CAACvC,MAAMC,IAAI,EAAEP,GAAG8C,IAAI,EAAEC,IAAI,CAC1D,IAAM,MACN,IAAM;YAER,IAAI,CAACH,YAAY,OAAO/C,UAAUoC;YAElC,IAAI;oBACuB3B,aAAiBA;gBAA1CL,aAAaK,MAAMC,IAAI,EAAED,CAAAA,cAAAA,MAAMgB,IAAI,YAAVhB,cAAc,GAAGA,CAAAA,gBAAAA,MAAMiB,MAAM,YAAZjB,gBAAgB;YAC5D,EAAE,OAAO0C,KAAK;gBACZC,QAAQC,GAAG,CAAC,4BAA4BF;gBACxC,OAAOrD,oBAAoBsC;YAC7B;YAEApC,UAAUoC;QACZ;IACF;AACF"}